{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/AndroidstudioSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/AndroidstudioSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/AndroidstudioSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/AndroidstudioSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-0df1a4b159eab89db82e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-36a9dbae596752c23fc8.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-cae7572e55216adea62d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-36a9dbae596752c23fc8.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-cae7572e55216adea62d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-0df1a4b159eab89db82e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}