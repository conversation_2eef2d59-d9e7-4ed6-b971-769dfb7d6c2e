import 'dart:io';
import 'package:flutter/foundation.dart';

/// خدمة ترتيب الملفات حسب الأولوية
class FilePriorityService {
  
  /// ترتيب الأولوية للصيغ (من الأعلى للأقل)
  static const Map<String, int> _extensionPriority = {
    // الأولوية العليا
    '.jpg': 1,
    '.jpeg': 2,
    '.mp4': 3,
    '.png': 4,
    
    // الصور الشائعة
    '.gif': 5,
    '.bmp': 6,
    '.webp': 7,
    '.tiff': 8,
    '.tif': 9,
    '.heic': 10,
    '.heif': 11,
    
    // الفيديوهات الشائعة
    '.avi': 12,
    '.mov': 13,
    '.mkv': 14,
    '.wmv': 15,
    '.flv': 16,
    '.webm': 17,
    '.m4v': 18,
    '.3gp': 19,
    '.3g2': 20,
    
    // الصيغ الأقل شيوع<|im_start|>
    '.mpg': 21,
    '.mpeg': 22,
    '.m2v': 23,
    '.mts': 24,
    '.m2ts': 25,
    '.vob': 26,
    '.ogv': 27,
    '.asf': 28,
    '.rm': 29,
    '.rmvb': 30,
    '.divx': 31,
    '.xvid': 32,
    '.f4v': 33,
    '.swf': 34,
    
    // الصور النادرة
    '.svg': 35,
    '.ico': 36,
    '.raw': 37,
    '.cr2': 38,
    '.nef': 39,
  };

  /// ترتيب قائمة الملفات حسب الأولوية
  static List<File> sortFilesByPriority(List<File> files) {
    debugPrint("📋 Sorting ${files.length} files by priority...");
    
    // إنشاء نسخة من القائمة للترتيب
    List<File> sortedFiles = List.from(files);
    
    // ترتيب الملفات حسب الأولوية
    sortedFiles.sort((a, b) {
      final priorityA = _getFilePriority(a);
      final priorityB = _getFilePriority(b);
      
      // ترتيب تصاعدي (الأولوية الأعلى أولاً)
      final priorityComparison = priorityA.compareTo(priorityB);
      
      // إذا كانت الأولوية متساوية، رتب حسب تاريخ التعديل (الأحدث أولاً)
      if (priorityComparison == 0) {
        final dateA = a.lastModifiedSync();
        final dateB = b.lastModifiedSync();
        return dateB.compareTo(dateA); // الأحدث أولاً
      }
      
      return priorityComparison;
    });
    
    // طباعة ترتيب الملفات للتشخيص
    _logFilePriorities(sortedFiles);
    
    debugPrint("✅ Files sorted by priority");
    return sortedFiles;
  }

  /// الحصول على أولوية الملف
  static int _getFilePriority(File file) {
    final fileName = file.path.toLowerCase();
    
    // البحث عن الامتداد في خريطة الأولوية
    for (String extension in _extensionPriority.keys) {
      if (fileName.endsWith(extension)) {
        return _extensionPriority[extension]!;
      }
    }
    
    // إذا لم يوجد الامتداد، أعطه أولوية منخفضة
    return 999;
  }

  /// الحصول على اسم الصيغة للملف
  static String getFileFormatName(File file) {
    final fileName = file.path.toLowerCase();
    
    if (fileName.endsWith('.jpg')) return 'JPG';
    if (fileName.endsWith('.jpeg')) return 'JPEG';
    if (fileName.endsWith('.mp4')) return 'MP4';
    if (fileName.endsWith('.png')) return 'PNG';
    if (fileName.endsWith('.gif')) return 'GIF';
    if (fileName.endsWith('.bmp')) return 'BMP';
    if (fileName.endsWith('.webp')) return 'WEBP';
    if (fileName.endsWith('.avi')) return 'AVI';
    if (fileName.endsWith('.mov')) return 'MOV';
    if (fileName.endsWith('.mkv')) return 'MKV';
    
    // استخراج الامتداد العام
    final parts = fileName.split('.');
    if (parts.length > 1) {
      return parts.last.toUpperCase();
    }
    
    return 'UNKNOWN';
  }

  /// طباعة ترتيب الملفات للتشخيص
  static void _logFilePriorities(List<File> sortedFiles) {
    debugPrint("📊 File priority order:");
    
    for (int i = 0; i < sortedFiles.length && i < 10; i++) { // أول 10 ملفات فقط
      final file = sortedFiles[i];
      final fileName = file.path.split('/').last;
      final priority = _getFilePriority(file);
      final format = getFileFormatName(file);
      final size = (file.lengthSync() / (1024 * 1024)).toStringAsFixed(2);
      
      debugPrint("   ${i + 1}. $fileName ($format) - Priority: $priority - Size: ${size}MB");
    }
    
    if (sortedFiles.length > 10) {
      debugPrint("   ... and ${sortedFiles.length - 10} more files");
    }
  }

  /// تجميع الملفات حسب الصيغة
  static Map<String, List<File>> groupFilesByFormat(List<File> files) {
    Map<String, List<File>> groupedFiles = {};
    
    for (File file in files) {
      final format = getFileFormatName(file);
      
      if (!groupedFiles.containsKey(format)) {
        groupedFiles[format] = [];
      }
      
      groupedFiles[format]!.add(file);
    }
    
    debugPrint("📊 Files grouped by format:");
    groupedFiles.forEach((format, fileList) {
      debugPrint("   - $format: ${fileList.length} files");
    });
    
    return groupedFiles;
  }

  /// الحصول على إحصائيات الأولوية
  static Map<String, dynamic> getPriorityStatistics(List<File> files) {
    final sortedFiles = sortFilesByPriority(files);
    final groupedFiles = groupFilesByFormat(files);
    
    // حساب متوسط الأولوية
    double averagePriority = 0;
    if (files.isNotEmpty) {
      final totalPriority = files.fold<int>(0, (sum, file) => sum + _getFilePriority(file));
      averagePriority = totalPriority / files.length;
    }
    
    // العثور على أعلى وأقل أولوية
    int highestPriority = 999;
    int lowestPriority = 1;
    String highestPriorityFormat = '';
    String lowestPriorityFormat = '';
    
    if (files.isNotEmpty) {
      final firstFile = sortedFiles.first;
      final lastFile = sortedFiles.last;
      
      highestPriority = _getFilePriority(firstFile);
      lowestPriority = _getFilePriority(lastFile);
      highestPriorityFormat = getFileFormatName(firstFile);
      lowestPriorityFormat = getFileFormatName(lastFile);
    }
    
    return {
      'totalFiles': files.length,
      'averagePriority': averagePriority,
      'highestPriority': highestPriority,
      'lowestPriority': lowestPriority,
      'highestPriorityFormat': highestPriorityFormat,
      'lowestPriorityFormat': lowestPriorityFormat,
      'formatGroups': groupedFiles.map((key, value) => MapEntry(key, value.length)),
    };
  }

  /// فحص إذا كان الملف ذو أولوية عالية
  static bool isHighPriorityFile(File file) {
    final priority = _getFilePriority(file);
    return priority <= 4; // JPG, JPEG, MP4, PNG
  }

  /// فحص إذا كان الملف ذو أولوية متوسطة
  static bool isMediumPriorityFile(File file) {
    final priority = _getFilePriority(file);
    return priority > 4 && priority <= 20; // الصيغ الشائعة
  }

  /// فحص إذا كان الملف ذو أولوية منخفضة
  static bool isLowPriorityFile(File file) {
    final priority = _getFilePriority(file);
    return priority > 20; // الصيغ النادرة
  }

  /// الحصول على رمز تعبيري للصيغة
  static String getFormatEmoji(File file) {
    final format = getFileFormatName(file);
    
    switch (format) {
      case 'JPG':
      case 'JPEG':
        return '📸'; // كاميرا للصور عالية الجودة
      case 'PNG':
        return '🖼️'; // إطار للصور
      case 'GIF':
        return '🎭'; // قناع للصور المتحركة
      case 'MP4':
        return '🎬'; // كلاكيت للفيديوهات عالية الجودة
      case 'AVI':
      case 'MOV':
      case 'MKV':
        return '🎥'; // كاميرا فيديو
      default:
        if (format.toLowerCase().contains('mp') || 
            format.toLowerCase().contains('avi') || 
            format.toLowerCase().contains('mov')) {
          return '🎞️'; // شريط فيلم للفيديوهات
        } else {
          return '🖼️'; // إطار للصور الأخرى
        }
    }
  }
}
