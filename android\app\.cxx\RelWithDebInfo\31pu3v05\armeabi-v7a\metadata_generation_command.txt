                        -HD:\def\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=D:\AndroidstudioSDK\ndk\25.1.8937393
-DCMAKE_ANDROID_NDK=D:\AndroidstudioSDK\ndk\25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=D:\AndroidstudioSDK\ndk\25.1.8937393\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\AndroidstudioSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\flutter_application_2\build\app\intermediates\cxx\RelWithDebInfo\31pu3v05\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\flutter_application_2\build\app\intermediates\cxx\RelWithDebInfo\31pu3v05\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\flutter_application_2\android\app\.cxx\RelWithDebInfo\31pu3v05\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2