{"buildFiles": ["D:\\def\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter_application_2\\android\\app\\.cxx\\Debug\\3jeh5061\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter_application_2\\android\\app\\.cxx\\Debug\\3jeh5061\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\AndroidstudioSDK\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\AndroidstudioSDK\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}