// This file acts as a placeholder for flutter_pdfview when running on the web.
// It provides dummy implementations to satisfy the compiler during conditional import.

import 'package:flutter/material.dart';

// Dummy PDFViewController class
class PDFViewController {
  // Add dummy methods or properties if PdfViewerScreen tries to call them
  // For now, an empty class might suffice if no methods are called directly
  // on the controller instance within the web-specific code path.
}

// Dummy FitPolicy enum (if needed by the calling code, though it's likely only used within PDFView)
enum FitPolicy { both } // Add other values if necessary

// Dummy PDFView widget for web
class PDFView extends StatelessWidget {
  final String? filePath; // Accept parameters even if unused
  final int defaultPage;
  final bool enableSwipe;
  final bool swipeHorizontal;
  final bool autoSpacing;
  final bool pageFling;
  final bool pageSnap;
  final FitPolicy fitPolicy;
  final bool preventLinkNavigation;
  final Function(int?)? onRender;
  final Function(dynamic)? onError;
  final Function(int?, dynamic)? onPageError;
  final Function(PDFViewController)? onViewCreated;
  final Function(int?, int?)? onPageChanged;


  const PDFView({
    super.key,
    this.filePath, // The real PDFView uses filePath, keep it for signature match
    this.defaultPage = 0,
    this.enableSwipe = true,
    this.swipeHorizontal = false,
    this.autoSpacing = false,
    this.pageFling = true,
    this.pageSnap = true,
    this.fitPolicy = FitPolicy.both,
    this.preventLinkNavigation = false,
    this.onRender,
    this.onError,
    this.onPageError,
    this.onViewCreated,
    this.onPageChanged,
    // Add any other parameters used by PdfViewerScreen to avoid compile errors
  });

  @override
  Widget build(BuildContext context) {
    // Display a simple message indicating PDF view is not available on web
    return const Center(
      child: Text(
        'PDF viewing is not supported on the web version.',
        style: TextStyle(fontSize: 16, color: Colors.grey),
        textAlign: TextAlign.center,
      ),
    );
  }
}
