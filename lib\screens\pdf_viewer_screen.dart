import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:async';
import 'dart:io';
import 'login_screen.dart';
import '../services/notification_service.dart';

class PdfViewerScreen extends StatefulWidget {
  final VoidCallback onThemeToggle;

  const PdfViewerScreen({super.key, required this.onThemeToggle});

  @override
  State<PdfViewerScreen> createState() => _PdfViewerScreenState();
}

class _PdfViewerScreenState extends State<PdfViewerScreen> {
  PDFViewController? _pdfController;
  int _currentPage = 0;
  int _totalPages = 0;
  bool _isReady = false;
  String? _errorMessage;
  String? _pdfPath;
  Timer? _saveTimer;
  final _storage = const FlutterSecureStorage();

  @override
  void initState() {
    super.initState();
    _loadLastPage();
    _startAutoSave();
    _loadPdfFromAssets();

    // فحص الإشعارات بعد تحميل الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      NotificationService.checkNotificationsOnStartup(context);
    });
  }

  Future<void> _loadPdfFromAssets() async {
    try {
      // تحميل ملف PDF من assets
      final byteData = await rootBundle.load('assets/النسخة الأولى.pdf');
      final bytes = byteData.buffer.asUint8List();

      // إنشاء ملف مؤقت
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/النسخة الأولى.pdf');
      await tempFile.writeAsBytes(bytes);

      setState(() {
        _pdfPath = tempFile.path;
        _isReady = true;
      });

      debugPrint("PDF loaded successfully from assets: ${tempFile.path}");
    } catch (e) {
      setState(() {
        _errorMessage = 'فشل في تحميل ملف PDF من الأصول: $e';
      });
      debugPrint("Error loading PDF from assets: $e");
    }
  }

  @override
  void dispose() {
    _saveTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadLastPage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastPage = prefs.getInt('last_page') ?? 0;
      setState(() {
        _currentPage = lastPage;
      });
    } catch (e) {
      debugPrint("Error loading last page: $e");
    }
  }

  Future<void> _saveCurrentPage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('last_page', _currentPage);
    } catch (e) {
      debugPrint("Error saving current page: $e");
    }
  }

  void _startAutoSave() {
    _saveTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _saveCurrentPage();
    });
  }

  Future<void> _logout() async {
    try {
      // Clear stored credentials
      await _storage.delete(key: 'user_email');
      await _storage.delete(key: 'user_password');

      // Clear saved page
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('last_page');

      // Navigate to login screen
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => LoginScreen(onThemeToggle: widget.onThemeToggle),
          ),
        );
      }
    } catch (e) {
      debugPrint("Error during logout: $e");
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تسجيل الخروج'),
          content: const Text('هل أنت متأكد من أنك تريد تسجيل الخروج؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _logout();
              },
              child: const Text('تسجيل الخروج'),
            ),
          ],
        );
      },
    );
  }



  void _goToPage() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final controller = TextEditingController();
        return AlertDialog(
          title: const Text('الانتقال إلى صفحة'),
          content: TextField(
            controller: controller,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'رقم الصفحة (1-$_totalPages)',
              border: const OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                final pageNumber = int.tryParse(controller.text);
                if (pageNumber != null && pageNumber >= 1 && pageNumber <= _totalPages) {
                  _pdfController?.setPage(pageNumber - 1);
                  Navigator.of(context).pop();
                }
              },
              child: const Text('انتقال'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Novel Reader Pro'),
          actions: [
            StreamBuilder<List<ConnectivityResult>>(
              stream: Connectivity().onConnectivityChanged,
              builder: (context, snapshot) {
                final results = snapshot.data ?? [];
                final isConnected = results.isNotEmpty && results.first != ConnectivityResult.none;
                return Icon(
                  Icons.wifi,
                  color: isConnected ? Colors.green : Colors.red,
                );
              },
            ),
            IconButton(
              icon: Icon(
                Theme.of(context).brightness == Brightness.dark
                    ? Icons.light_mode
                    : Icons.dark_mode,
              ),
              onPressed: widget.onThemeToggle,
            ),
          ],
        ),
        body: const Center(
          child: Padding(
            padding: EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.web,
                  size: 80,
                  color: Colors.orange,
                ),
                SizedBox(height: 24),
                Text(
                  'عذراً، عارض PDF غير متاح على الويب',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16),
                Text(
                  'يرجى استخدام التطبيق على الهاتف المحمول للاستفادة من جميع الميزات',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Novel Reader Pro'),
        actions: [
          // WiFi status indicator
          StreamBuilder<List<ConnectivityResult>>(
            stream: Connectivity().onConnectivityChanged,
            builder: (context, snapshot) {
              final results = snapshot.data ?? [];
              final isConnected = results.isNotEmpty && results.first != ConnectivityResult.none;
              return Icon(
                Icons.wifi,
                color: isConnected ? Colors.green : Colors.red,
              );
            },
          ),
          // Go to page button
          if (_isReady && _totalPages > 0)
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: _goToPage,
            ),

          // Theme toggle button
          IconButton(
            icon: Icon(
              Theme.of(context).brightness == Brightness.dark
                  ? Icons.light_mode
                  : Icons.dark_mode,
            ),
            onPressed: widget.onThemeToggle,
          ),
          // Logout button
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _showLogoutDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Page indicator
          if (_isReady && _totalPages > 0)
            Container(
              padding: const EdgeInsets.all(8.0),
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'الصفحة ${_currentPage + 1} من $_totalPages',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

          // PDF Viewer
          Expanded(
            child: _errorMessage != null
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error,
                            size: 80,
                            color: Colors.red,
                          ),
                          const SizedBox(height: 24),
                          const Text(
                            'خطأ في تحميل الملف',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _errorMessage!,
                            style: const TextStyle(fontSize: 16),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  )
                : _pdfPath != null ? PDFView(
                    filePath: _pdfPath!,
                    enableSwipe: true,
                    swipeHorizontal: false,
                    autoSpacing: false,
                    pageFling: true,
                    pageSnap: true,
                    defaultPage: _currentPage,
                    fitPolicy: FitPolicy.BOTH,
                    preventLinkNavigation: false,
                    onRender: (pages) {
                      setState(() {
                        _totalPages = pages ?? 0;
                        _isReady = true;
                      });
                    },
                    onError: (error) {
                      setState(() {
                        _errorMessage = 'فشل في تحميل ملف PDF: $error';
                      });
                    },
                    onPageError: (page, error) {
                      setState(() {
                        _errorMessage = 'خطأ في الصفحة $page: $error';
                      });
                    },
                    onViewCreated: (PDFViewController pdfViewController) {
                      _pdfController = pdfViewController;
                    },
                    onLinkHandler: (String? uri) {
                      debugPrint('Link: $uri');
                    },
                    onPageChanged: (int? page, int? total) {
                      setState(() {
                        _currentPage = page ?? 0;
                      });
                    },
                  ) : const Center(
                    child: CircularProgressIndicator(),
                  ),
          ),
        ],
      ),
    );
  }
}
