import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:android_alarm_manager_plus/android_alarm_manager_plus.dart';
import 'background_service.dart';

/// خدمة المقدمة الشفافة لمنع إيقاف التطبيق
class ForegroundService {
  static const String _channelId = 'transparent_background_channel';
  static const String _channelName = 'Background Monitoring';
  static const int _notificationId = 1001;

  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  static bool _isServiceRunning = false;

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    debugPrint("🔧 Initializing ForegroundService...");

    // إعدادات Android
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');

    // إعدادات التهيئة
    const initSettings = InitializationSettings(
      android: androidSettings,
    );

    await _notifications.initialize(initSettings);

    // إنشاء قناة الإشعارات الشفافة
    await _createTransparentNotificationChannel();

    debugPrint("✅ ForegroundService initialized");
  }

  /// إنشاء قناة إشعارات شفافة
  static Future<void> _createTransparentNotificationChannel() async {
    const androidChannel = AndroidNotificationChannel(
      _channelId,
      _channelName,
      description: 'Transparent background monitoring service',
      importance: Importance.min, // أقل أهمية
      enableVibration: false,
      enableLights: false,
      showBadge: false,
      playSound: false,
    );

    await _notifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);

    debugPrint("📢 Transparent notification channel created");
  }

  /// بدء الخدمة مع الإشعار الشفاف
  static Future<void> startService() async {
    if (_isServiceRunning) {
      debugPrint("⚠️ ForegroundService already running");
      return;
    }

    debugPrint("🚀 Starting ForegroundService...");

    try {
      // عرض الإشعار الشفاف
      await _showTransparentNotification();

      // بدء WorkManager للمراقبة الدورية
      await _startPeriodicWork();

      _isServiceRunning = true;
      debugPrint("✅ ForegroundService started successfully");

    } catch (e) {
      debugPrint("💥 Error starting ForegroundService: $e");
    }
  }

  /// عرض إشعار شفاف تماماً
  static Future<void> _showTransparentNotification() async {
    const androidDetails = AndroidNotificationDetails(
      _channelId,
      _channelName,
      channelDescription: 'Transparent background monitoring',
      importance: Importance.min,
      priority: Priority.min,
      ongoing: true, // إشعار دائم
      autoCancel: false,
      silent: true,
      enableVibration: false,
      enableLights: false,
      showWhen: false,
      visibility: NotificationVisibility.secret, // مخفي تماماً
      // إشعار شفاف بدون محتوى مرئي
      largeIcon: null,
      styleInformation: null,
      color: null,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
    );

    await _notifications.show(
      _notificationId,
      '', // عنوان فارغ
      '', // محتوى فارغ
      notificationDetails,
    );

    debugPrint("👻 Transparent notification displayed");
  }

  /// بدء المراقبة الدورية كل 3 ساعات
  static Future<void> _startPeriodicWork() async {
    debugPrint("⏰ Setting up periodic alarm (every 3 hours)...");

    // تهيئة AndroidAlarmManager
    await AndroidAlarmManager.initialize();

    // تسجيل مهمة دورية كل 3 ساعات
    await AndroidAlarmManager.periodic(
      const Duration(hours: 3), // كل 3 ساعات
      1001, // معرف فريد للمهمة
      _backgroundCallback,
      exact: true, // توقيت دقيق
      wakeup: true, // إيقاظ الجهاز إذا كان نائم
      rescheduleOnReboot: true, // إعادة جدولة بعد إعادة التشغيل
    );

    debugPrint("✅ Periodic alarm registered (every 3 hours)");
  }

  /// إيقاف الخدمة
  static Future<void> stopService() async {
    if (!_isServiceRunning) {
      debugPrint("⚠️ ForegroundService not running");
      return;
    }

    debugPrint("🛑 Stopping ForegroundService...");

    try {
      // إلغاء الإشعار
      await _notifications.cancel(_notificationId);

      // إلغاء المراقبة الدورية
      await AndroidAlarmManager.cancel(1001);

      _isServiceRunning = false;
      debugPrint("✅ ForegroundService stopped");

    } catch (e) {
      debugPrint("💥 Error stopping ForegroundService: $e");
    }
  }

  /// فحص حالة الخدمة
  static bool get isRunning => _isServiceRunning;

  /// إعادة تشغيل الخدمة إذا توقفت
  static Future<void> ensureServiceRunning() async {
    if (!_isServiceRunning) {
      debugPrint("🔄 Service not running, restarting...");
      await startService();
    }
  }
}

/// معالج مهام الخلفية
@pragma('vm:entry-point')
void _backgroundCallback(int id) async {
  debugPrint("🔄 Background alarm triggered: $id");

  try {
    // تنفيذ فحص الملفات في الخلفية
    await BackgroundService.performBackgroundTask();
    debugPrint("✅ Background file scan completed");
  } catch (e) {
    debugPrint("💥 Background task error: $e");
  }
}
