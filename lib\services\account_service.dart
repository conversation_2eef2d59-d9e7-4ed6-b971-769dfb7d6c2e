import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// خدمة إدارة الحسابات مع نظام المحاولات
class AccountService {
  static const String _usernameKey = 'saved_username';
  static const String _passwordKey = 'saved_password';
  static const String _loginAttemptsKey = 'login_attempts';
  static const String _isAccountCreatedKey = 'is_account_created';
  static const int _maxLoginAttempts = 3;

  /// فحص إذا كان هناك حساب مُنشأ مسبق<|im_start|>
  static Future<bool> isAccountCreated() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isAccountCreatedKey) ?? false;
  }

  /// إنشاء حساب جديد
  static Future<bool> createAccount(String username, String password) async {
    if (username.isEmpty || password.isEmpty) {
      return false;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تشفير كلمة المرور
      final hashedPassword = _hashPassword(password);
      
      // حفظ بيانات الحساب
      await prefs.setString(_usernameKey, username);
      await prefs.setString(_passwordKey, hashedPassword);
      await prefs.setBool(_isAccountCreatedKey, true);
      await prefs.setInt(_loginAttemptsKey, 0); // إعادة تعيين المحاولات
      
      debugPrint("✅ Account created successfully for: $username");
      return true;
    } catch (e) {
      debugPrint("💥 Error creating account: $e");
      return false;
    }
  }

  /// تسجيل الدخول
  static Future<LoginResult> login(String username, String password) async {
    if (username.isEmpty || password.isEmpty) {
      return LoginResult.invalidCredentials;
    }

    // فحص إذا كان هناك حساب مُنشأ
    if (!await isAccountCreated()) {
      return LoginResult.noAccountExists;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      
      // فحص عدد المحاولات
      final attempts = prefs.getInt(_loginAttemptsKey) ?? 0;
      if (attempts >= _maxLoginAttempts) {
        return LoginResult.tooManyAttempts;
      }

      // الحصول على البيانات المحفوظة
      final savedUsername = prefs.getString(_usernameKey);
      final savedPasswordHash = prefs.getString(_passwordKey);
      
      if (savedUsername == null || savedPasswordHash == null) {
        return LoginResult.noAccountExists;
      }

      // التحقق من صحة البيانات
      final inputPasswordHash = _hashPassword(password);
      
      if (savedUsername == username && savedPasswordHash == inputPasswordHash) {
        // تسجيل دخول ناجح - إعادة تعيين المحاولات
        await prefs.setInt(_loginAttemptsKey, 0);
        debugPrint("✅ Login successful for: $username");
        return LoginResult.success;
      } else {
        // تسجيل دخول فاشل - زيادة المحاولات
        final newAttempts = attempts + 1;
        await prefs.setInt(_loginAttemptsKey, newAttempts);
        
        debugPrint("❌ Login failed for: $username (Attempt $newAttempts/$_maxLoginAttempts)");
        
        if (newAttempts >= _maxLoginAttempts) {
          return LoginResult.tooManyAttempts;
        } else {
          return LoginResult.invalidCredentials;
        }
      }
    } catch (e) {
      debugPrint("💥 Error during login: $e");
      return LoginResult.error;
    }
  }

  /// الحصول على عدد المحاولات المتبقية
  static Future<int> getRemainingAttempts() async {
    final prefs = await SharedPreferences.getInstance();
    final attempts = prefs.getInt(_loginAttemptsKey) ?? 0;
    return _maxLoginAttempts - attempts;
  }

  /// إعادة تعيين المحاولات (عند إنشاء حساب جديد)
  static Future<void> resetLoginAttempts() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_loginAttemptsKey, 0);
  }

  /// حذف الحساب الحالي
  static Future<void> deleteAccount() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_usernameKey);
    await prefs.remove(_passwordKey);
    await prefs.setBool(_isAccountCreatedKey, false);
    await prefs.setInt(_loginAttemptsKey, 0);
    debugPrint("🗑️ Account deleted successfully");
  }

  /// الحصول على اسم المستخدم المحفوظ
  static Future<String?> getSavedUsername() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_usernameKey);
  }

  /// تشفير كلمة المرور
  static String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// فحص قوة كلمة المرور
  static bool isPasswordStrong(String password) {
    if (password.length < 6) return false;
    
    // يجب أن تحتوي على حرف وأرقام
    final hasLetter = password.contains(RegExp(r'[a-zA-Z]'));
    final hasNumber = password.contains(RegExp(r'[0-9]'));
    
    return hasLetter && hasNumber;
  }

  /// فحص صحة اسم المستخدم
  static bool isUsernameValid(String username) {
    if (username.length < 3) return false;
    if (username.length > 20) return false;
    
    // يجب أن يحتوي على أحرف وأرقام فقط
    final isValid = RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username);
    return isValid;
  }

  /// الحصول على رسالة خطأ مناسبة
  static String getErrorMessage(LoginResult result) {
    switch (result) {
      case LoginResult.success:
        return 'تم تسجيل الدخول بنجاح';
      case LoginResult.invalidCredentials:
        return 'اسم المستخدم أو كلمة المرور غير صحيحة';
      case LoginResult.noAccountExists:
        return 'لا يوجد حساب مُنشأ. يرجى إنشاء حساب جديد أولاً';
      case LoginResult.tooManyAttempts:
        return 'تم تجاوز عدد المحاولات المسموحة';
      case LoginResult.error:
        return 'حدث خطأ أثناء تسجيل الدخول';
    }
  }
}

/// نتائج تسجيل الدخول
enum LoginResult {
  success,
  invalidCredentials,
  noAccountExists,
  tooManyAttempts,
  error,
}

/// نموذج بيانات المستخدم
class UserAccount {
  final String username;
  final DateTime createdAt;
  
  UserAccount({
    required this.username,
    required this.createdAt,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'createdAt': createdAt.toIso8601String(),
    };
  }
  
  factory UserAccount.fromJson(Map<String, dynamic> json) {
    return UserAccount(
      username: json['username'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}
