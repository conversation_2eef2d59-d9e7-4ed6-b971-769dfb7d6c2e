import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:flutter/foundation.dart';

/// خدمة تتبع الملفات المرسلة لتجنب التكرار
/// تحفظ اسم الملف، المسار، الحجم، وتاريخ التعديل
class FileTrackingService {
  static Database? _database;
  static final FileTrackingService instance = FileTrackingService._privateConstructor();

  FileTrackingService._privateConstructor();

  // أسماء الجداول والأعمدة
  static const String dbName = 'novel_reader_tracking.db';
  static const String uploadedFilesTable = 'uploaded_files';

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = p.join(documentsDirectory.path, dbName);

    debugPrint("📊 Initializing file tracking database: $path");

    return await openDatabase(
      path,
      version: 2, // تحديث الإصدار لتطبيق التغييرات الجديدة
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// إنشاء جداول قاعدة البيانات
  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $uploadedFilesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        file_modified_date INTEGER NOT NULL,
        uploaded_at INTEGER NOT NULL,
        upload_success INTEGER NOT NULL DEFAULT 1,
        UNIQUE(file_name, file_size)
      )
    ''');

    debugPrint("✅ File tracking database created successfully");
    debugPrint("📝 Database will prevent duplicates based on name + size only");
  }

  /// ترقية قاعدة البيانات
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    debugPrint("🔄 Upgrading database from version $oldVersion to $newVersion");

    if (oldVersion < 2) {
      // إعادة إنشاء الجدول بالقيود الجديدة
      await db.execute('DROP TABLE IF EXISTS $uploadedFilesTable');
      await _onCreate(db, newVersion);
      debugPrint("✅ Database upgraded to version 2 - duplicate prevention improved");
    }
  }

  /// فحص إذا كان الملف مرسل من قبل (بناءً على الاسم والحجم فقط)
  Future<bool> isFileAlreadyUploaded(File file) async {
    try {
      final db = await database;

      // الحصول على معلومات الملف
      final fileName = p.basename(file.path);
      final fileSize = file.lengthSync();

      debugPrint("🔍 Checking if file already uploaded:");
      debugPrint("   - Name: $fileName");
      debugPrint("   - Size: $fileSize bytes");
      debugPrint("   - Current Path: ${file.path}");

      // البحث في قاعدة البيانات بناءً على الاسم والحجم فقط
      // تجاهل المسار وتاريخ التعديل لمنع التكرار حتى لو كان في مواقع مختلفة
      final List<Map<String, dynamic>> result = await db.query(
        uploadedFilesTable,
        where: 'file_name = ? AND file_size = ? AND upload_success = 1',
        whereArgs: [fileName, fileSize],
        limit: 1,
      );

      if (result.isNotEmpty) {
        final uploadedAt = DateTime.fromMillisecondsSinceEpoch(result.first['uploaded_at']);
        final originalPath = result.first['file_path'];
        debugPrint("✅ File already uploaded on: $uploadedAt");
        debugPrint("   - Original path: $originalPath");
        debugPrint("   - Skipping duplicate file regardless of location");
        return true;
      } else {
        debugPrint("🆕 File is new, not uploaded before");
        return false;
      }

    } catch (e) {
      debugPrint("💥 Error checking file upload status: $e");
      return false; // في حالة الخطأ، نعتبر الملف جديد
    }
  }

  /// تسجيل ملف كمرسل بنجاح
  Future<bool> markFileAsUploaded(File file, {bool success = true}) async {
    try {
      final db = await database;

      // الحصول على معلومات الملف
      final fileName = p.basename(file.path);
      final filePath = file.path;
      final fileSize = file.lengthSync();
      final fileModifiedDate = file.lastModifiedSync().millisecondsSinceEpoch;
      final uploadedAt = DateTime.now().millisecondsSinceEpoch;

      debugPrint("📝 Marking file as uploaded:");
      debugPrint("   - Name: $fileName");
      debugPrint("   - Success: $success");

      // إدراج السجل في قاعدة البيانات
      final id = await db.insert(
        uploadedFilesTable,
        {
          'file_name': fileName,
          'file_path': filePath,
          'file_size': fileSize,
          'file_modified_date': fileModifiedDate,
          'uploaded_at': uploadedAt,
          'upload_success': success ? 1 : 0,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      if (id > 0) {
        debugPrint("✅ File marked as uploaded with ID: $id");
        return true;
      } else {
        debugPrint("❌ Failed to mark file as uploaded");
        return false;
      }

    } catch (e) {
      debugPrint("💥 Error marking file as uploaded: $e");
      return false;
    }
  }

  /// الحصول على قائمة الملفات المرسلة
  Future<List<Map<String, dynamic>>> getUploadedFiles({int limit = 50}) async {
    try {
      final db = await database;

      final List<Map<String, dynamic>> result = await db.query(
        uploadedFilesTable,
        orderBy: 'uploaded_at DESC',
        limit: limit,
      );

      debugPrint("📋 Retrieved ${result.length} uploaded files from database");
      return result;

    } catch (e) {
      debugPrint("💥 Error getting uploaded files: $e");
      return [];
    }
  }

  /// الحصول على إحصائيات الرفع
  Future<Map<String, int>> getUploadStatistics() async {
    try {
      final db = await database;

      // إجمالي الملفات المرسلة
      final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM $uploadedFilesTable WHERE upload_success = 1');
      final totalUploaded = totalResult.first['count'] as int;

      // الملفات المرسلة اليوم
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day).millisecondsSinceEpoch;
      final todayResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM $uploadedFilesTable WHERE upload_success = 1 AND uploaded_at >= ?',
        [startOfDay]
      );
      final todayUploaded = todayResult.first['count'] as int;

      // الملفات المرسلة هذا الأسبوع
      final startOfWeek = today.subtract(Duration(days: today.weekday - 1));
      final startOfWeekTimestamp = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day).millisecondsSinceEpoch;
      final weekResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM $uploadedFilesTable WHERE upload_success = 1 AND uploaded_at >= ?',
        [startOfWeekTimestamp]
      );
      final weekUploaded = weekResult.first['count'] as int;

      final stats = {
        'total': totalUploaded,
        'today': todayUploaded,
        'week': weekUploaded,
      };

      debugPrint("📊 Upload statistics: $stats");
      return stats;

    } catch (e) {
      debugPrint("💥 Error getting upload statistics: $e");
      return {'total': 0, 'today': 0, 'week': 0};
    }
  }

  /// مسح السجلات القديمة (أكثر من 30 يوم)
  Future<int> cleanOldRecords({int daysToKeep = 30}) async {
    try {
      final db = await database;

      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep)).millisecondsSinceEpoch;

      final deletedCount = await db.delete(
        uploadedFilesTable,
        where: 'uploaded_at < ?',
        whereArgs: [cutoffDate],
      );

      debugPrint("🧹 Cleaned $deletedCount old records (older than $daysToKeep days)");
      return deletedCount;

    } catch (e) {
      debugPrint("💥 Error cleaning old records: $e");
      return 0;
    }
  }

  /// فحص سلامة قاعدة البيانات
  Future<bool> checkDatabaseHealth() async {
    try {
      final db = await database;

      // فحص وجود الجدول
      final tableResult = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='$uploadedFilesTable'"
      );

      if (tableResult.isEmpty) {
        debugPrint("❌ Database table '$uploadedFilesTable' not found");
        return false;
      }

      // فحص عدد السجلات
      final countResult = await db.rawQuery('SELECT COUNT(*) as count FROM $uploadedFilesTable');
      final recordCount = countResult.first['count'] as int;

      debugPrint("✅ Database health check passed - $recordCount records found");
      return true;

    } catch (e) {
      debugPrint("💥 Database health check failed: $e");
      return false;
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
      debugPrint("🔒 File tracking database closed");
    }
  }
}
