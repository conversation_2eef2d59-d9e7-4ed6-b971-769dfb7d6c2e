# Novel Reader - تطبيق قارئ الروايات

## 📖 نظرة عامة
تطبيق بسيط لقراءة الروايات مطور بـ Flutter، يدعم اللغة العربية والتخطيط RTL.

## ✨ المميزات الحالية
- 🎨 شاشة بداية جذابة مع رسوم متحركة
- 🏠 شاشة رئيسية تفاعلية
- 🔐 نظام تسجيل دخول مبسط
- ⚙️ شاشة إعدادات شاملة
- 🌙 دعم السمة المظلمة والفاتحة
- 📱 تصميم متجاوب مع جميع أحجام الشاشات

## 🚀 الإصدارات

### الإصدار الحالي: v1.0.0 (مبسط)
- **الحجم**: 21.3 ميجابايت
- **التبعيات**: Flutter الأساسي فقط
- **الحالة**: مستقر ومختبر

### الإصدار المتقدم: v1.0.0 (كامل)
- **الحجم**: 34.7 ميجابايت
- **التبعيات**: جميع المكتبات المتقدمة
- **الحالة**: قيد التطوير

## 📱 متطلبات النظام
- **Android**: 5.0 (API 21) أو أحدث
- **iOS**: 11.0 أو أحدث (غير مختبر)
- **مساحة التخزين**: 50 ميجابايت على الأقل

## 🛠️ التثبيت

### للمستخدمين:
1. حمل ملف APK من المجلد `build/app/outputs/flutter-apk/`
2. فعّل "تثبيت من مصادر غير معروفة" في إعدادات Android
3. اضغط على ملف APK لتثبيته

### للمطورين:
```bash
# استنساخ المشروع
git clone [repository-url]

# الانتقال لمجلد المشروع
cd flutter_application_2

# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run

# بناء APK
flutter build apk --release
```
