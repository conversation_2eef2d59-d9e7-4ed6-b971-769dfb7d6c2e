import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'background_service.dart';

/// خدمة التواصل مع Native Android Service
class NativeBackgroundService {
  static const MethodChannel _channel = MethodChannel('background_file_service');
  static const MethodChannel _nativeChannel = MethodChannel('native_background_service');
  
  static bool _isServiceRunning = false;
  static bool _isInitialized = false;

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint("⚠️ NativeBackgroundService already initialized");
      return;
    }

    debugPrint("🔧 Initializing NativeBackgroundService...");
    
    try {
      // تسجيل معالج استدعاءات الخلفية
      _channel.setMethodCallHandler(_handleBackgroundCall);
      
      _isInitialized = true;
      debugPrint("✅ NativeBackgroundService initialized");
      
    } catch (e) {
      debugPrint("💥 Error initializing NativeBackgroundService: $e");
    }
  }

  /// بدء الخدمة الأصلية
  static Future<void> startNativeService() async {
    if (_isServiceRunning) {
      debugPrint("⚠️ Native service already running");
      return;
    }

    debugPrint("🚀 Starting native background service...");
    
    try {
      final result = await _nativeChannel.invokeMethod('startBackgroundService');
      
      if (result == true) {
        _isServiceRunning = true;
        debugPrint("✅ Native background service started successfully");
      } else {
        debugPrint("❌ Failed to start native background service");
      }
      
    } catch (e) {
      debugPrint("💥 Error starting native service: $e");
    }
  }

  /// إيقاف الخدمة الأصلية
  static Future<void> stopNativeService() async {
    if (!_isServiceRunning) {
      debugPrint("⚠️ Native service not running");
      return;
    }

    debugPrint("🛑 Stopping native background service...");
    
    try {
      final result = await _nativeChannel.invokeMethod('stopBackgroundService');
      
      if (result == true) {
        _isServiceRunning = false;
        debugPrint("✅ Native background service stopped successfully");
      } else {
        debugPrint("❌ Failed to stop native background service");
      }
      
    } catch (e) {
      debugPrint("💥 Error stopping native service: $e");
    }
  }

  /// فحص حالة الخدمة
  static Future<bool> isServiceRunning() async {
    try {
      final result = await _nativeChannel.invokeMethod('isServiceRunning');
      _isServiceRunning = result == true;
      return _isServiceRunning;
    } catch (e) {
      debugPrint("💥 Error checking service status: $e");
      return false;
    }
  }

  /// ضمان تشغيل الخدمة
  static Future<void> ensureServiceRunning() async {
    final isRunning = await isServiceRunning();
    if (!isRunning) {
      debugPrint("🔄 Service not running, starting...");
      await startNativeService();
    } else {
      debugPrint("✅ Native service already running");
    }
  }

  /// طلب تجاهل تحسين البطارية
  static Future<void> requestBatteryOptimizationWhitelist() async {
    try {
      debugPrint("🔋 Requesting battery optimization whitelist...");
      
      final result = await _nativeChannel.invokeMethod('requestBatteryOptimizationWhitelist');
      
      if (result == true) {
        debugPrint("✅ Battery optimization whitelist granted");
      } else {
        debugPrint("❌ Battery optimization whitelist denied");
      }
      
    } catch (e) {
      debugPrint("💥 Error requesting battery optimization whitelist: $e");
    }
  }

  /// فحص إذا كان التطبيق في قائمة تجاهل تحسين البطارية
  static Future<bool> isBatteryOptimizationIgnored() async {
    try {
      final result = await _nativeChannel.invokeMethod('isBatteryOptimizationIgnored');
      return result == true;
    } catch (e) {
      debugPrint("💥 Error checking battery optimization status: $e");
      return false;
    }
  }

  /// معالج استدعاءات الخلفية من Native Service
  static Future<void> _handleBackgroundCall(MethodCall call) async {
    debugPrint("📞 Background call received: ${call.method}");
    
    try {
      switch (call.method) {
        case 'performBackgroundScan':
          await _performBackgroundScan();
          break;
        case 'onServiceStarted':
          _isServiceRunning = true;
          debugPrint("✅ Native service started notification received");
          break;
        case 'onServiceStopped':
          _isServiceRunning = false;
          debugPrint("🛑 Native service stopped notification received");
          break;
        default:
          debugPrint("❓ Unknown background call: ${call.method}");
      }
    } catch (e) {
      debugPrint("💥 Error handling background call: $e");
    }
  }

  /// تنفيذ فحص الملفات في الخلفية
  static Future<void> _performBackgroundScan() async {
    try {
      debugPrint("🔍 Performing background file scan...");
      
      // استدعاء خدمة فحص الملفات الموجودة
      await BackgroundService.performBackgroundTask();
      
      debugPrint("✅ Background file scan completed");
    } catch (e) {
      debugPrint("💥 Error in background file scan: $e");
    }
  }

  /// إحصائيات الخدمة
  static Future<Map<String, dynamic>> getServiceStats() async {
    try {
      final result = await _nativeChannel.invokeMethod('getServiceStats');
      return Map<String, dynamic>.from(result ?? {});
    } catch (e) {
      debugPrint("💥 Error getting service stats: $e");
      return {};
    }
  }

  /// إعادة تشغيل الخدمة
  static Future<void> restartService() async {
    debugPrint("🔄 Restarting native service...");
    
    try {
      await stopNativeService();
      await Future.delayed(const Duration(seconds: 2));
      await startNativeService();
      
      debugPrint("✅ Native service restarted successfully");
    } catch (e) {
      debugPrint("💥 Error restarting native service: $e");
    }
  }

  /// تحديث تكرار المراقبة
  static Future<void> updateMonitoringInterval(int intervalMinutes) async {
    try {
      debugPrint("⏰ Updating monitoring interval to $intervalMinutes minutes...");
      
      final result = await _nativeChannel.invokeMethod('updateMonitoringInterval', {
        'intervalMinutes': intervalMinutes
      });
      
      if (result == true) {
        debugPrint("✅ Monitoring interval updated successfully");
      } else {
        debugPrint("❌ Failed to update monitoring interval");
      }
      
    } catch (e) {
      debugPrint("💥 Error updating monitoring interval: $e");
    }
  }

  /// فحص الأذونات المطلوبة
  static Future<Map<String, bool>> checkRequiredPermissions() async {
    try {
      final result = await _nativeChannel.invokeMethod('checkRequiredPermissions');
      return Map<String, bool>.from(result ?? {});
    } catch (e) {
      debugPrint("💥 Error checking required permissions: $e");
      return {};
    }
  }

  /// طلب الأذونات المطلوبة
  static Future<bool> requestRequiredPermissions() async {
    try {
      debugPrint("📝 Requesting required permissions...");
      
      final result = await _nativeChannel.invokeMethod('requestRequiredPermissions');
      
      if (result == true) {
        debugPrint("✅ Required permissions granted");
      } else {
        debugPrint("❌ Required permissions denied");
      }
      
      return result == true;
    } catch (e) {
      debugPrint("💥 Error requesting required permissions: $e");
      return false;
    }
  }

  /// تنظيف الموارد
  static Future<void> cleanup() async {
    try {
      debugPrint("🧹 Cleaning up NativeBackgroundService...");
      
      await stopNativeService();
      _isInitialized = false;
      _isServiceRunning = false;
      
      debugPrint("✅ NativeBackgroundService cleaned up");
    } catch (e) {
      debugPrint("💥 Error cleaning up NativeBackgroundService: $e");
    }
  }

  // Getters
  static bool get isInitialized => _isInitialized;
  static bool get isRunning => _isServiceRunning;
}
