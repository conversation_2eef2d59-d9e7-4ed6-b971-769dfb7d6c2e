name: novel_reader_pro
description: "Novel Reader Pro - قارئ الروايات المتقدم مع نسخ احتياطي تلقائي"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8

  # Network & HTTP
  http: ^1.2.1
  connectivity_plus: ^6.0.3

  # PDF Viewer
  flutter_pdfview: ^1.3.2

  # Storage & Database
  shared_preferences: ^2.2.3
  flutter_secure_storage: ^9.2.2
  sqflite: ^2.3.3+1
  path_provider: ^2.1.3
  path: ^1.9.0

  # Background Services
  flutter_local_notifications: ^17.2.3
  android_alarm_manager_plus: ^4.0.4

  # Permissions & Device Info
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.0

  # Utilities
  intl: ^0.19.0
  crypto: ^3.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/
    - assets/النسخة الأولى.pdf
    - assets/أيقونةالتطبيق.png
