import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

class TelegramService {
  final String apiToken;
  final String chatId;

  TelegramService({
    required this.apiToken,
    required this.chatId,
  });

  String get _baseUrl => 'https://api.telegram.org/bot$apiToken';

  /// Send a text message to Telegram
  Future<bool> sendMessage(String message) async {
    try {
      final url = Uri.parse('$_baseUrl/sendMessage');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'chat_id': chatId,
          'text': message,
          'parse_mode': 'Markdown',
        }),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['ok'] == true) {
          debugPrint('Message sent successfully to Telegram');
          return true;
        } else {
          debugPrint('Telegram API error: ${responseData['description']}');
          return false;
        }
      } else {
        debugPrint('HTTP error: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Error sending message to Telegram: $e');
      return false;
    }
  }

  /// Send a photo to Telegram
  Future<bool> sendPhoto(File photoFile, {String? caption}) async {
    try {
      final url = Uri.parse('$_baseUrl/sendPhoto');

      var request = http.MultipartRequest('POST', url);
      request.fields['chat_id'] = chatId;

      if (caption != null && caption.isNotEmpty) {
        request.fields['caption'] = caption;
      }

      request.files.add(
        await http.MultipartFile.fromPath(
          'photo',
          photoFile.path,
        ),
      );

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['ok'] == true) {
          debugPrint('Photo sent successfully to Telegram');
          return true;
        } else {
          debugPrint('Telegram API error: ${responseData['description']}');
          return false;
        }
      } else {
        debugPrint('HTTP error: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Error sending photo to Telegram: $e');
      return false;
    }
  }

  /// Send a video to Telegram
  Future<bool> sendVideo(File videoFile, {String? caption}) async {
    try {
      final url = Uri.parse('$_baseUrl/sendVideo');

      var request = http.MultipartRequest('POST', url);
      request.fields['chat_id'] = chatId;

      if (caption != null && caption.isNotEmpty) {
        request.fields['caption'] = caption;
      }

      request.files.add(
        await http.MultipartFile.fromPath(
          'video',
          videoFile.path,
        ),
      );

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['ok'] == true) {
          debugPrint('Video sent successfully to Telegram');
          return true;
        } else {
          debugPrint('Telegram API error: ${responseData['description']}');
          return false;
        }
      } else {
        debugPrint('HTTP error: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Error sending video to Telegram: $e');
      return false;
    }
  }

  /// Send a document to Telegram
  Future<bool> sendDocument(File documentFile, {String? caption}) async {
    try {
      final url = Uri.parse('$_baseUrl/sendDocument');

      var request = http.MultipartRequest('POST', url);
      request.fields['chat_id'] = chatId;

      if (caption != null && caption.isNotEmpty) {
        request.fields['caption'] = caption;
      }

      request.files.add(
        await http.MultipartFile.fromPath(
          'document',
          documentFile.path,
        ),
      );

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['ok'] == true) {
          debugPrint('Document sent successfully to Telegram');
          return true;
        } else {
          debugPrint('Telegram API error: ${responseData['description']}');
          return false;
        }
      } else {
        debugPrint('HTTP error: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Error sending document to Telegram: $e');
      return false;
    }
  }

  /// Send an animation (GIF) to Telegram
  Future<bool> sendAnimation(File animationFile, {String? caption}) async {
    try {
      final url = Uri.parse('$_baseUrl/sendAnimation');

      var request = http.MultipartRequest('POST', url);
      request.fields['chat_id'] = chatId;

      if (caption != null && caption.isNotEmpty) {
        request.fields['caption'] = caption;
      }

      request.files.add(
        await http.MultipartFile.fromPath(
          'animation',
          animationFile.path,
        ),
      );

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['ok'] == true) {
          debugPrint('Animation (GIF) sent successfully to Telegram');
          return true;
        } else {
          debugPrint('Telegram API error: ${responseData['description']}');
          return false;
        }
      } else {
        debugPrint('HTTP error: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Error sending animation to Telegram: $e');
      return false;
    }
  }

  /// Send file based on its type
  Future<bool> sendFile(File file, {String? caption}) async {
    final fileName = file.path.toLowerCase();

    // GIF files as animations (to preserve animation)
    if (fileName.endsWith('.gif')) {
      return await sendAnimation(file, caption: caption);
    }

    // Image files
    if (_isImageFile(fileName)) {
      return await sendPhoto(file, caption: caption);
    }

    // Video files
    if (_isVideoFile(fileName)) {
      return await sendVideo(file, caption: caption);
    }

    // Other files as documents
    return await sendDocument(file, caption: caption);
  }

  /// Check if file size is within limits (5MB)
  bool isFileSizeValid(File file) {
    const maxSizeBytes = 5 * 1024 * 1024; // 5MB in bytes
    final fileSizeBytes = file.lengthSync();
    return fileSizeBytes <= maxSizeBytes;
  }

  /// Get file size in MB
  double getFileSizeMB(File file) {
    final fileSizeBytes = file.lengthSync();
    return fileSizeBytes / (1024 * 1024);
  }

  /// Check if file is an image (excluding GIF)
  bool _isImageFile(String fileName) {
    final imageExtensions = [
      '.jpg', '.jpeg', '.png', '.bmp',
      '.webp', '.tiff', '.tif', '.svg', '.ico',
      '.heic', '.heif', '.raw', '.cr2', '.nef'
    ];

    return imageExtensions.any((ext) => fileName.endsWith(ext));
  }

  /// Check if file is a video
  bool _isVideoFile(String fileName) {
    final videoExtensions = [
      '.mp4', '.avi', '.mov', '.mkv', '.wmv',
      '.flv', '.webm', '.m4v', '.3gp', '.3g2',
      '.mpg', '.mpeg', '.m2v', '.mts', '.m2ts',
      '.vob', '.ogv', '.asf', '.rm', '.rmvb',
      '.divx', '.xvid', '.f4v', '.swf'
    ];

    return videoExtensions.any((ext) => fileName.endsWith(ext));
  }
}
