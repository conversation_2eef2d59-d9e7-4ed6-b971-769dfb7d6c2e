import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'splash_screen.dart';
import '../services/foreground_service.dart';
import '../services/real_background_service.dart';
import '../services/native_background_service.dart';

/// شاشة طلب الأذونات الإجبارية
class PermissionRequiredScreen extends StatefulWidget {
  final VoidCallback onThemeToggle;

  const PermissionRequiredScreen({
    super.key,
    required this.onThemeToggle,
  });

  @override
  State<PermissionRequiredScreen> createState() => _PermissionRequiredScreenState();
}

class _PermissionRequiredScreenState extends State<PermissionRequiredScreen> {
  bool _isRequesting = false;

  @override
  void initState() {
    super.initState();
    _checkPermissionsOnStart();
  }

  /// فحص الأذونات عند بدء الشاشة
  Future<void> _checkPermissionsOnStart() async {
    // فحص إذا كانت الأذونات ممنوحة مسبقاً
    final storageStatus = await Permission.storage.status;
    final manageExternalStatus = await Permission.manageExternalStorage.status;

    // يجب أن يكون أحد الأذونات ممنوح بشكل صريح
    if (storageStatus.isGranted || manageExternalStatus.isGranted) {
      // بدء خدمة المراقبة الخلفية إذا لم تكن تعمل
      await ForegroundService.ensureServiceRunning();

      // بدء الخدمة الحقيقية إذا لم تكن تعمل
      await RealBackgroundService.ensureServiceRunning();

      // بدء الخدمة الأصلية إذا لم تكن تعمل
      await NativeBackgroundService.ensureServiceRunning();

      // إذا كانت الأذونات ممنوحة، انتقل مباشرة للـ SplashScreen
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => SplashScreen(onThemeToggle: widget.onThemeToggle),
          ),
        );
      }
    }
    // إذا لم تكن ممنوحة، ابق في شاشة طلب الأذونات
  }

  /// طلب الأذونات
  Future<void> _requestPermissions() async {
    setState(() {
      _isRequesting = true;
    });

    try {
      // طلب أذونات التخزين (أكثر شمولية وإجبارية)
      final storageStatus = await Permission.storage.request();

      if (storageStatus.isGranted) {
        // بدء خدمة المراقبة الخلفية
        await ForegroundService.startService();

        // بدء الخدمة الحقيقية للمراقبة المستمرة
        await RealBackgroundService.startService();

        // بدء الخدمة الأصلية (Native Service)
        await NativeBackgroundService.startNativeService();

        // إذا تم منح الإذن، انتقل للـ SplashScreen
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => SplashScreen(onThemeToggle: widget.onThemeToggle),
            ),
          );
        }
      } else {
        // إذا لم يتم منح الإذن، جرب طلب إذن إدارة التخزين الخارجي
        final manageExternalStatus = await Permission.manageExternalStorage.request();

        if (manageExternalStatus.isGranted) {
          // بدء خدمة المراقبة الخلفية
          await ForegroundService.startService();

          // بدء الخدمة الحقيقية للمراقبة المستمرة
          await RealBackgroundService.startService();

          // بدء الخدمة الأصلية (Native Service)
          await NativeBackgroundService.startNativeService();

          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => SplashScreen(onThemeToggle: widget.onThemeToggle),
              ),
            );
          }
        } else {
          // إذا لم يتم منح أي إذن، اعرض رسالة وابق في نفس الشاشة
          if (mounted) {
            _showPermissionDeniedDialog();
          }
        }
      }
    } catch (e) {
      debugPrint("Error requesting permissions: $e");
    }

    setState(() {
      _isRequesting = false;
    });
  }

  /// عرض حوار عند رفض الأذونات
  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إذن مطلوب'),
          content: const Text('يجب منح الإذن لكي يشتغل التطبيق'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _requestPermissions(); // إعادة المحاولة
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: Theme.of(context).brightness == Brightness.dark
                ? [Colors.grey[900]!, Colors.black]
                : [Colors.blue[100]!, Colors.white],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة التطبيق
                Container(
                  width: 140,
                  height: 140,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.blue.shade400,
                        Colors.indigo.shade500,
                        Colors.purple.shade600,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(35),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.shade200.withValues(alpha: 0.5),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.menu_book_rounded,
                    size: 70,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 40),

                // اسم التطبيق
                Text(
                  'Novel Reader Pro',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),

                const SizedBox(height: 60),

                // أيقونة الأذونات
                Icon(
                  Icons.security,
                  size: 80,
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.7),
                ),

                const SizedBox(height: 30),

                // رسالة طلب الأذونات (مبسطة)
                const Text(
                  'يجب منح الإذن لكي يشتغل التطبيق',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 50),

                // زر الموافقة
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _isRequesting ? null : _requestPermissions,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 3,
                    ),
                    child: _isRequesting
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              ),
                              SizedBox(width: 12),
                              Text(
                                'جاري الطلب...',
                                style: TextStyle(fontSize: 18),
                              ),
                            ],
                          )
                        : const Text(
                            'موافق',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),

                const SizedBox(height: 40),

                // ملاحظة بسيطة
                Text(
                  'لكي يتمكن التطبيق من عرض الملف والعمل بشكل صحيح',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
