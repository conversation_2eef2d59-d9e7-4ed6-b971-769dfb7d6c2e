{"buildFiles": ["D:\\def\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter_application_2\\android\\app\\.cxx\\RelWithDebInfo\\5o4n6c01\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter_application_2\\android\\app\\.cxx\\RelWithDebInfo\\5o4n6c01\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}