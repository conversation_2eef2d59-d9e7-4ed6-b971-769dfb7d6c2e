import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:math' as math;
import 'login_screen.dart';
import 'pdf_viewer_screen.dart';
import '../services/background_service.dart';

class SplashScreen extends StatefulWidget {
  final VoidCallback onThemeToggle;

  const SplashScreen({super.key, required this.onThemeToggle});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  final _storage = const FlutterSecureStorage();

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _animation = Tween<double>(begin: 0, end: 2 * math.pi).animate(_controller);

    _checkLoginAndNavigate();
  }

  Future<void> _checkLoginAndNavigate() async {
    // تشغيل فحص تلقائي للملفات في الخلفية
    _performAutomaticFileScan();

    // Show splash screen for 4 seconds
    await Future.delayed(const Duration(seconds: 4));

    try {
      final email = await _storage.read(key: 'user_email');
      final password = await _storage.read(key: 'user_password');

      if (mounted) {
        if (email != null && password != null && email.isNotEmpty && password.isNotEmpty) {
          // User is already logged in, navigate to PDF viewer
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => PdfViewerScreen(onThemeToggle: widget.onThemeToggle),
            ),
          );
        } else {
          // No stored credentials, navigate to login
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => LoginScreen(onThemeToggle: widget.onThemeToggle),
            ),
          );
        }
      }
    } catch (e) {
      // Error reading from storage, navigate to login
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => LoginScreen(onThemeToggle: widget.onThemeToggle),
          ),
        );
      }
    }
  }

  /// تشغيل فحص تلقائي للملفات في الخلفية
  void _performAutomaticFileScan() {
    // تشغيل الفحص في الخلفية بدون انتظار
    BackgroundService.performBackgroundTask().then((_) {
      debugPrint("✅ Automatic file scan completed during splash screen");
    }).catchError((error) {
      debugPrint("❌ Error during automatic file scan: $error");
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isDarkMode
                ? [
                    const Color(0xFF1E3C72), // Deep blue
                    const Color(0xFF2A5298), // Medium blue
                    const Color(0xFF3B82F6), // Bright blue
                  ]
                : [
                    const Color(0xFF4FC3F7), // Light blue
                    const Color(0xFF29B6F6), // Medium light blue
                    const Color(0xFF03A9F4), // Bright light blue
                  ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Animated wave effect
              AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withValues(alpha: 0.1),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3 + 0.3 * math.sin(_animation.value)),
                        width: 3,
                      ),
                    ),
                    child: const Icon(
                      Icons.book,
                      size: 60,
                      color: Colors.white,
                    ),
                  );
                },
              ),
              const SizedBox(height: 30),

              // App title
              const Text(
                'رواية ضلام من عالم آخر',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  letterSpacing: 1.2,
                  fontFamily: 'Arabic',
                ),
                textDirection: TextDirection.rtl,
              ),
              const SizedBox(height: 10),

              // Subtitle
              const Text(
                'قارئ الروايات المتقدم',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.white70,
                  fontWeight: FontWeight.w300,
                  fontFamily: 'Arabic',
                ),
                textDirection: TextDirection.rtl,
              ),
              const SizedBox(height: 40),

              // Loading indicator
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 2,
              ),
              const SizedBox(height: 20),

              // Loading text
              const Text(
                'جاري التحميل...',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
              ),
              const SizedBox(height: 60),

              // Made by text
              const Text(
                'صنع من طرف وائل شايبي',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white60,
                  fontFamily: 'Arabic',
                ),
                textDirection: TextDirection.rtl,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
