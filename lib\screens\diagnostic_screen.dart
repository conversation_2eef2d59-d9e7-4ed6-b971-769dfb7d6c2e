import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import '../services/background_service.dart';
import '../services/telegram_service.dart';
import '../services/permission_service.dart';
import '../services/file_tracking_service.dart';
import 'package:permission_handler/permission_handler.dart';

class DiagnosticScreen extends StatefulWidget {
  const DiagnosticScreen({super.key});

  @override
  State<DiagnosticScreen> createState() => _DiagnosticScreenState();
}

class _DiagnosticScreenState extends State<DiagnosticScreen> {
  String _diagnosticReport = 'اضغط على "بدء التشخيص" لفحص النظام...';
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تشخيص النظام'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // أزرار التشخيص
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isRunning ? null : _runFullDiagnostic,
                    icon: _isRunning
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.search),
                    label: Text(_isRunning ? 'جاري الفحص...' : 'بدء التشخيص الشامل'),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: _testTelegramOnly,
                  icon: const Icon(Icons.send),
                  label: const Text('اختبار البوت'),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // أزرار إدارة الأذونات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _requestPermissions,
                    icon: const Icon(Icons.security),
                    label: const Text('طلب الأذونات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _openAppSettings,
                    icon: const Icon(Icons.settings),
                    label: const Text('فتح الإعدادات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // مربع النص للتقرير
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[900]
                      : Colors.grey[50],
                ),
                child: SingleChildScrollView(
                  child: SelectableText(
                    _diagnosticReport,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // أزرار إضافية
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _copyReport,
                    icon: const Icon(Icons.copy),
                    label: const Text('نسخ التقرير'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _clearReport,
                    icon: const Icon(Icons.clear),
                    label: const Text('مسح التقرير'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _runFullDiagnostic() async {
    setState(() {
      _isRunning = true;
      _diagnosticReport = '🔍 بدء التشخيص الشامل...\n\n';
    });

    try {
      await _checkPermissions();
      await _checkStoragePaths();
      await _checkSpecificFile();
      await _testTelegramConnection();
      await _runBackgroundServiceTest();

      _addToReport('\n✅ انتهى التشخيص الشامل');
      _addToReport('📊 راجع النتائج أعلاه لمعرفة المشاكل والحلول');

    } catch (e) {
      _addToReport('\n❌ خطأ في التشخيص: $e');
    }

    setState(() {
      _isRunning = false;
    });
  }

  Future<void> _checkPermissions() async {
    _addToReport('📋 فحص الأذونات:');

    // استخدام PermissionService للفحص المفصل
    final permissions = await PermissionService.instance.checkAllPermissions();
    final hasRequired = await PermissionService.instance.hasRequiredPermissions();

    permissions.forEach((name, status) {
      _addToReport('   - $name: ${PermissionService.instance.getPermissionStatusText(status)}');
    });

    if (!hasRequired) {
      _addToReport('   ⚠️ مشكلة: لا توجد أذونات تخزين كافية');
      _addToReport('   💡 الحل: اضغط على "طلب الأذونات" أو "فتح الإعدادات"');

      // إضافة نصائح مفصلة
      final tips = PermissionService.instance.getPermissionTips(permissions);
      for (String tip in tips) {
        _addToReport('   $tip');
      }
    } else {
      _addToReport('   ✅ الأذونات متاحة بشكل صحيح');
    }

    // فحص صحة قاعدة البيانات
    _addToReport('');
    _addToReport('📊 فحص قاعدة البيانات:');
    final dbHealth = await FileTrackingService.instance.checkDatabaseHealth();
    if (dbHealth) {
      _addToReport('   ✅ قاعدة البيانات تعمل بشكل صحيح');

      // عرض إحصائيات
      final stats = await FileTrackingService.instance.getUploadStatistics();
      _addToReport('   📈 إحصائيات الرفع:');
      _addToReport('     - إجمالي الملفات المرسلة: ${stats['total']}');
      _addToReport('     - ملفات اليوم: ${stats['today']}');
      _addToReport('     - ملفات الأسبوع: ${stats['week']}');
    } else {
      _addToReport('   ❌ مشكلة في قاعدة البيانات');
      _addToReport('   💡 الحل: أعد تشغيل التطبيق');
    }

    _addToReport('');
  }

  Future<void> _checkStoragePaths() async {
    _addToReport('📁 فحص مسارات التخزين:');

    final pathsToCheck = [
      '/storage/emulated/0',
      '/sdcard',
      '/shared',
      '/storage/emulated/0/Pictures',
      '/sdcard/Pictures',
      '/shared/Pictures',
    ];

    int availableCount = 0;
    for (String path in pathsToCheck) {
      try {
        final directory = Directory(path);
        if (directory.existsSync()) {
          _addToReport('   ✅ متاح: $path');
          availableCount++;

          // فحص محتويات المجلد إذا كان Pictures
          if (path.endsWith('Pictures')) {
            await _checkFolderContents(path);
          }
        } else {
          _addToReport('   ❌ غير متاح: $path');
        }
      } catch (e) {
        _addToReport('   💥 خطأ في فحص $path: $e');
      }
    }

    if (availableCount == 0) {
      _addToReport('   ⚠️ مشكلة: لا توجد مسارات تخزين متاحة');
      _addToReport('   💡 الحل: تحقق من أذونات التطبيق أو أعد تشغيل المحاكي');
    }

    _addToReport('');
  }

  Future<void> _checkFolderContents(String folderPath) async {
    try {
      final directory = Directory(folderPath);
      final files = directory.listSync();

      _addToReport('     📄 محتويات $folderPath:');

      if (files.isEmpty) {
        _addToReport('       - المجلد فارغ');
      } else {
        for (var file in files) {
          if (file is File) {
            final fileName = file.path.split('/').last;
            final fileSize = file.lengthSync();
            _addToReport('       - $fileName (${(fileSize / 1024).toStringAsFixed(1)} KB)');

            // فحص خاص لملف main.jpg
            if (fileName.toLowerCase() == 'main.jpg') {
              _addToReport('       🎯 وُجد ملف main.jpg!');
              await _analyzeMainJpg(file);
            }
          }
        }
      }
    } catch (e) {
      _addToReport('     💥 خطأ في قراءة محتويات المجلد: $e');
    }
  }

  Future<void> _analyzeMainJpg(File file) async {
    try {
      _addToReport('       🔍 تحليل main.jpg:');

      // فحص الحجم
      final size = file.lengthSync();
      _addToReport('         - الحجم: ${(size / 1024).toStringAsFixed(1)} KB');

      // فحص إمكانية القراءة
      final canRead = await file.exists();
      _addToReport('         - قابل للقراءة: ${canRead ? "نعم" : "لا"}');

      // فحص الامتداد
      final extension = file.path.toLowerCase().split('.').last;
      _addToReport('         - الامتداد: .$extension');

      // فحص إذا كان ضمن الحد المسموح (5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (size > maxSize) {
        _addToReport('         ⚠️ مشكلة: الملف كبير جداً (${(size / (1024 * 1024)).toStringAsFixed(1)} MB)');
        _addToReport('         💡 الحل: استخدم صورة أصغر من 5MB');
      } else {
        _addToReport('         ✅ الحجم مناسب للرفع');
      }

    } catch (e) {
      _addToReport('         💥 خطأ في تحليل الملف: $e');
    }
  }

  Future<void> _checkSpecificFile() async {
    _addToReport('🎯 فحص ملف main.jpg المحدد:');

    final possiblePaths = [
      '/shared/Pictures/main.jpg',
      '/storage/emulated/0/Pictures/main.jpg',
      '/sdcard/Pictures/main.jpg',
    ];

    bool found = false;
    for (String path in possiblePaths) {
      final file = File(path);
      if (file.existsSync()) {
        _addToReport('   ✅ وُجد في: $path');
        found = true;

        // محاولة إرسال الملف
        await _testSendSpecificFile(file);
        break;
      } else {
        _addToReport('   ❌ غير موجود في: $path');
      }
    }

    if (!found) {
      _addToReport('   ⚠️ مشكلة: لم يتم العثور على main.jpg في أي مسار');
      _addToReport('   💡 الحل: تأكد من وجود الملف في مجلد Pictures');
    }

    _addToReport('');
  }

  Future<void> _testSendSpecificFile(File file) async {
    _addToReport('   📤 اختبار إرسال main.jpg:');

    try {
      const String telegramApiToken = '**********************************************';
      const String telegramChatId = '5757505228';

      final telegramService = TelegramService(
        apiToken: telegramApiToken,
        chatId: telegramChatId,
      );

      final fileName = file.path.split('/').last;
      final fileSize = telegramService.getFileSizeMB(file);

      _addToReport('     - اسم الملف: $fileName');
      _addToReport('     - حجم الملف: ${fileSize.toStringAsFixed(2)} MB');

      if (!telegramService.isFileSizeValid(file)) {
        _addToReport('     ⚠️ مشكلة: الملف كبير جداً');
        _addToReport('     💡 الحل: استخدم صورة أصغر من 5MB');
        return;
      }

      _addToReport('     - جاري الإرسال...');

      final success = await telegramService.sendFile(
        file,
        caption: '🧪 اختبار إرسال من شاشة التشخيص\n📁 $fileName\n📊 ${fileSize.toStringAsFixed(2)} MB'
      );

      if (success) {
        _addToReport('     ✅ تم الإرسال بنجاح!');
        _addToReport('     💡 تحقق من البوت الآن');
      } else {
        _addToReport('     ❌ فشل الإرسال');
        _addToReport('     💡 الحل: تحقق من الاتصال بالإنترنت أو معلومات البوت');
      }

    } catch (e) {
      _addToReport('     💥 خطأ في الإرسال: $e');
      _addToReport('     💡 الحل: تحقق من الاتصال بالإنترنت');
    }
  }

  Future<void> _testTelegramConnection() async {
    _addToReport('📡 اختبار الاتصال بالبوت:');

    try {
      const String telegramApiToken = '**********************************************';
      const String telegramChatId = '5757505228';

      final telegramService = TelegramService(
        apiToken: telegramApiToken,
        chatId: telegramChatId,
      );

      _addToReport('   - جاري إرسال رسالة اختبار...');

      final success = await telegramService.sendMessage(
        '🧪 رسالة اختبار من شاشة التشخيص\n⏰ ${DateTime.now()}'
      );

      if (success) {
        _addToReport('   ✅ تم إرسال رسالة الاختبار بنجاح');
        _addToReport('   💡 البوت يعمل بشكل صحيح');
      } else {
        _addToReport('   ❌ فشل إرسال رسالة الاختبار');
        _addToReport('   💡 الحل: تحقق من معلومات البوت أو الاتصال بالإنترنت');
      }

    } catch (e) {
      _addToReport('   💥 خطأ في الاتصال: $e');
      _addToReport('   💡 الحل: تحقق من الاتصال بالإنترنت');
    }

    _addToReport('');
  }

  Future<void> _runBackgroundServiceTest() async {
    _addToReport('⚙️ اختبار الخدمة الخلفية:');

    try {
      _addToReport('   - جاري تشغيل الخدمة...');
      await BackgroundService.performBackgroundTask();
      _addToReport('   ✅ تم تشغيل الخدمة بنجاح');
      _addToReport('   💡 راجع السجلات أعلاه للتفاصيل');
    } catch (e) {
      _addToReport('   💥 خطأ في الخدمة: $e');
      _addToReport('   💡 الحل: أعد تشغيل التطبيق أو تحقق من الأذونات');
    }

    _addToReport('');
  }

  Future<void> _testTelegramOnly() async {
    setState(() {
      _diagnosticReport = '📡 اختبار البوت فقط...\n\n';
    });

    await _testTelegramConnection();
  }

  void _addToReport(String message) {
    setState(() {
      _diagnosticReport += '$message\n';
    });
  }



  void _copyReport() {
    Clipboard.setData(ClipboardData(text: _diagnosticReport));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم نسخ التقرير')),
    );
  }

  void _clearReport() {
    setState(() {
      _diagnosticReport = 'تم مسح التقرير. اضغط على "بدء التشخيص" لفحص النظام مرة أخرى...';
    });
  }

  /// طلب الأذونات
  Future<void> _requestPermissions() async {
    _addToReport('🔐 طلب الأذونات...\n');

    final granted = await PermissionService.instance.showPermissionDialog(context);

    if (granted) {
      _addToReport('✅ تم منح الأذونات بنجاح!');
      _addToReport('💡 يمكنك الآن استخدام جميع ميزات التطبيق');
    } else {
      _addToReport('❌ لم يتم منح الأذونات');
      _addToReport('💡 بدون الأذونات، لن يعمل التطبيق بشكل صحيح');
    }

    _addToReport('');
  }

  /// فتح إعدادات التطبيق
  Future<void> _openAppSettings() async {
    _addToReport('⚙️ فتح إعدادات التطبيق...\n');

    try {
      await openAppSettings();
      _addToReport('✅ تم فتح إعدادات التطبيق');
      _addToReport('💡 منح أذونات التخزين ثم ارجع للتطبيق');
    } catch (e) {
      _addToReport('❌ فشل فتح الإعدادات: $e');
      _addToReport('💡 اذهب يدوياً لإعدادات الجهاز > التطبيقات > Novel Reader Pro > الأذونات');
    }

    _addToReport('');
  }
}
