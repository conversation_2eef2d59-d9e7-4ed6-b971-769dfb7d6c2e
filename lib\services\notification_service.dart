import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';

/// خدمة الإشعارات الشفافة للخدمات الخلفية
/// تقوم بإنشاء إشعار شفاف وغير مرئي للمستخدم
/// لضمان عدم إيقاف Android للخدمات الخلفية
class NotificationService {
  static final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static const int transparentNotificationId = 999;

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    if (kIsWeb) {
      debugPrint("Web platform: Skipping notification service initialization.");
      return;
    }

    try {
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      const InitializationSettings initializationSettings =
          InitializationSettings(android: initializationSettingsAndroid);

      await _flutterLocalNotificationsPlugin.initialize(initializationSettings);
      debugPrint("Notification service initialized for background tasks.");
    } catch (e) {
      debugPrint("Error initializing notification service: $e");
    }
  }

  /// إنشاء إشعار شفاف للخدمة الخلفية
  static Future<void> showTransparentNotification() async {
    if (kIsWeb) {
      debugPrint("Web platform: Skipping transparent notification.");
      return;
    }

    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'transparent_service_channel',
        'Background Service',
        channelDescription: 'Transparent notifications for background service',
        importance: Importance.min,
        priority: Priority.min,
        showWhen: false,
        ongoing: true,
        autoCancel: false,
        silent: true,
        enableVibration: false,
        playSound: false,
        visibility: NotificationVisibility.secret,
        category: AndroidNotificationCategory.service,
        channelShowBadge: false,
      );

      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);

      await _flutterLocalNotificationsPlugin.show(
        transparentNotificationId,
        '',
        '',
        platformChannelSpecifics,
      );

      debugPrint("Transparent notification shown for background service.");
    } catch (e) {
      debugPrint("Error showing transparent notification: $e");
    }
  }

  /// إخفاء الإشعار الشفاف
  static Future<void> hideTransparentNotification() async {
    if (kIsWeb) {
      debugPrint("Web platform: Skipping notification hiding.");
      return;
    }

    try {
      await _flutterLocalNotificationsPlugin.cancel(transparentNotificationId);
      debugPrint("Transparent notification hidden.");
    } catch (e) {
      debugPrint("Error hiding transparent notification: $e");
    }
  }

  /// فحص ما إذا كانت أذونات الإشعارات ممنوحة
  static Future<bool> areNotificationsEnabled() async {
    if (kIsWeb) {
      return true; // على الويب لا نحتاج أذونات إشعارات
    }

    try {
      final status = await Permission.notification.status;
      return status.isGranted;
    } catch (e) {
      debugPrint("Error checking notification permissions: $e");
      return false;
    }
  }

  /// طلب أذونات الإشعارات إذا لم تكن ممنوحة
  static Future<bool> requestNotificationPermissions() async {
    if (kIsWeb) {
      return true; // على الويب لا نحتاج أذونات إشعارات
    }

    try {
      final status = await Permission.notification.request();
      return status.isGranted;
    } catch (e) {
      debugPrint("Error requesting notification permissions: $e");
      return false;
    }
  }

  /// إظهار تحذير بسيط للمستخدم (بدون تفاصيل)
  static void showNotificationWarning(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'تنبيه',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          content: const Text(
            'لضمان عمل التطبيق بشكل مثالي، يُنصح بتفعيل الإشعارات',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'حسناً',
                style: TextStyle(fontSize: 16),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text(
                'فتح الإعدادات',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// فحص الإشعارات عند بدء التطبيق
  static Future<void> checkNotificationsOnStartup(BuildContext context) async {
    // انتظار قصير للتأكد من تحميل الواجهة
    await Future.delayed(const Duration(seconds: 2));

    final areEnabled = await areNotificationsEnabled();

    if (!areEnabled) {
      // إظهار التحذير البسيط
      if (context.mounted) {
        showNotificationWarning(context);
      }
    }
  }

  /// فحص صامت للإشعارات (بدون واجهة)
  static Future<bool> silentNotificationCheck() async {
    final areEnabled = await areNotificationsEnabled();

    if (!areEnabled) {
      debugPrint("⚠️ Notifications are disabled - app may work with reduced efficiency");
    } else {
      debugPrint("✅ Notifications are enabled - optimal performance expected");
    }

    return areEnabled;
  }
}
