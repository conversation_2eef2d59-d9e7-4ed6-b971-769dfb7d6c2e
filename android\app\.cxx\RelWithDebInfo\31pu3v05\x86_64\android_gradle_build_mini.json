{"buildFiles": ["D:\\def\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter_application_2\\android\\app\\.cxx\\RelWithDebInfo\\31pu3v05\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter_application_2\\android\\app\\.cxx\\RelWithDebInfo\\31pu3v05\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}