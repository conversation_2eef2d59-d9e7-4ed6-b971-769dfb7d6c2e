import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// خدمة إدارة الأذونات مع واجهات توجيهية
class PermissionService {
  static final PermissionService instance = PermissionService._privateConstructor();
  PermissionService._privateConstructor();

  /// فحص حالة جميع الأذونات المطلوبة
  Future<Map<String, PermissionStatus>> checkAllPermissions() async {
    debugPrint("🔍 Checking all required permissions...");

    final permissions = {
      'storage': await Permission.storage.status,
      'manageExternalStorage': await Permission.manageExternalStorage.status,
      'photos': await Permission.photos.status,
      'mediaLibrary': await Permission.mediaLibrary.status,
    };

    debugPrint("📋 Permission status:");
    permissions.forEach((name, status) {
      debugPrint("   - $name: $status");
    });

    return permissions;
  }

  /// فحص إذا كانت الأذونات الأساسية ممنوحة
  Future<bool> hasRequiredPermissions() async {
    final permissions = await checkAllPermissions();

    // نحتاج storage أو manageExternalStorage
    final hasStorageAccess = permissions['storage']?.isGranted == true ||
                            permissions['manageExternalStorage']?.isGranted == true;

    debugPrint("✅ Has required permissions: $hasStorageAccess");
    return hasStorageAccess;
  }

  /// طلب الأذونات المطلوبة
  Future<bool> requestPermissions() async {
    debugPrint("📝 Requesting permissions...");

    try {
      // طلب أذونات التخزين
      Map<Permission, PermissionStatus> statuses = await [
        Permission.storage,
        Permission.manageExternalStorage,
      ].request();

      debugPrint("📋 Permission request results:");
      statuses.forEach((permission, status) {
        debugPrint("   - $permission: $status");
      });

      // فحص إذا تم منح الأذونات الأساسية
      final hasAccess = statuses[Permission.storage]?.isGranted == true ||
                       statuses[Permission.manageExternalStorage]?.isGranted == true;

      if (hasAccess) {
        debugPrint("✅ Required permissions granted");
        return true;
      } else {
        debugPrint("❌ Required permissions not granted");
        return false;
      }

    } catch (e) {
      debugPrint("💥 Error requesting permissions: $e");
      return false;
    }
  }

  /// عرض حوار طلب الأذونات
  Future<bool> showPermissionDialog(BuildContext context) async {
    final hasPermissions = await hasRequiredPermissions();

    if (hasPermissions) {
      debugPrint("✅ Permissions already granted");
      return true;
    }

    // التحقق من mounted قبل استخدام context
    if (!context.mounted) return false;

    // عرض حوار توضيحي
    final shouldRequest = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('أذونات التخزين مطلوبة'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'يحتاج التطبيق إلى أذونات التخزين للعمل بشكل صحيح:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text('• قراءة الصور من مجلدات الجهاز'),
              Text('• رفع الصور الجديدة تلقائياً'),
              Text('• مراقبة المجلدات للتغييرات'),
              SizedBox(height: 12),
              Text(
                'بدون هذه الأذونات، لن يتمكن التطبيق من العمل.',
                style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              child: const Text('منح الأذونات'),
            ),
          ],
        );
      },
    );

    if (shouldRequest == true) {
      // طلب الأذونات
      final granted = await requestPermissions();

      if (!granted) {
        // إذا لم تُمنح الأذونات، عرض حوار الإعدادات
        if (context.mounted) {
          await _showSettingsDialog(context);
        }
      }

      return granted;
    }

    return false;
  }

  /// عرض حوار توجيه للإعدادات
  Future<void> _showSettingsDialog(BuildContext context) async {
    if (!context.mounted) return;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('أذونات مطلوبة'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'لم يتم منح أذونات التخزين. يرجى اتباع الخطوات التالية:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text('1. اضغط على "فتح الإعدادات"'),
              Text('2. اذهب إلى "الأذونات" أو "Permissions"'),
              Text('3. فعّل "التخزين" أو "Storage"'),
              Text('4. فعّل "إدارة جميع الملفات" إن وُجد'),
              Text('5. ارجع للتطبيق'),
              SizedBox(height: 12),
              Text(
                'بدون هذه الأذونات، لن يعمل التطبيق.',
                style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                openAppSettings();
              },
              child: const Text('فتح الإعدادات'),
            ),
          ],
        );
      },
    );
  }

  /// فتح إعدادات التطبيق
  Future<void> openAppSettings() async {
    try {
      debugPrint("🔧 Opening app settings...");
      await openAppSettings();
    } catch (e) {
      debugPrint("💥 Error opening app settings: $e");
    }
  }

  /// عرض حالة الأذونات بشكل مفصل
  String getPermissionStatusText(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'ممنوح ✅';
      case PermissionStatus.denied:
        return 'مرفوض ❌';
      case PermissionStatus.restricted:
        return 'مقيد ⚠️';
      case PermissionStatus.limited:
        return 'محدود ⚠️';
      case PermissionStatus.permanentlyDenied:
        return 'مرفوض نهائياً ❌';
      default:
        return 'غير معروف ❓';
    }
  }

  /// الحصول على نصائح لحل مشاكل الأذونات
  List<String> getPermissionTips(Map<String, PermissionStatus> permissions) {
    List<String> tips = [];

    final hasAnyStoragePermission = permissions['storage']?.isGranted == true ||
                                   permissions['manageExternalStorage']?.isGranted == true;

    if (!hasAnyStoragePermission) {
      tips.add('💡 اذهب لإعدادات التطبيق ومنح أذونات التخزين');
      tips.add('💡 فعّل "إدارة جميع الملفات" في إعدادات النظام');
      tips.add('💡 أعد تشغيل التطبيق بعد منح الأذونات');
    }

    if (permissions['storage'] == PermissionStatus.permanentlyDenied ||
        permissions['manageExternalStorage'] == PermissionStatus.permanentlyDenied) {
      tips.add('⚠️ تم رفض الأذونات نهائياً - يجب الذهاب للإعدادات يدوياً');
      tips.add('💡 إلغاء تثبيت التطبيق وإعادة تثبيته قد يحل المشكلة');
    }

    if (tips.isEmpty) {
      tips.add('✅ جميع الأذونات ممنوحة بشكل صحيح');
    }

    return tips;
  }

  /// فحص دوري للأذونات
  Future<void> periodicPermissionCheck() async {
    debugPrint("🔄 Performing periodic permission check...");

    final hasPermissions = await hasRequiredPermissions();

    if (!hasPermissions) {
      debugPrint("⚠️ Permissions lost or not granted");
      // يمكن إضافة إشعار للمستخدم هنا
    } else {
      debugPrint("✅ Permissions still valid");
    }
  }
}
